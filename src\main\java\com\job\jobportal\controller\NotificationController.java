package com.job.jobportal.controller;

import com.job.jobportal.dto.NotificationDTO;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class NotificationController {

    @Autowired
    NotificationService notificationService;

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);


    @PostMapping("/notifications/send")
    public ResponseEntity<?> sendNotification(@RequestBody NotificationDTO request) {
        try {
            notificationService.sendSingleNotification(request.getTitle(), request.getBody());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    "msg.dairynotes_details_added_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }

    @PostMapping("/notifications/broadcast")
    public ResponseEntity<?> sendBroadcast(@RequestBody NotificationDTO request) {
        try {
            notificationService.sendBroadcastNotification(request.getTitle(), request.getBody());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    "msg.dairynotes_details_added_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


}

