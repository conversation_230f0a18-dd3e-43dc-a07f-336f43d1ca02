package com.job.jobportal.model;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class UploadModal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long uploadId;

    private int uploadFileType;



    @Column(columnDefinition = "LONGTEXT")
    private String uploadUrl;

    private String uploadName;

    private Timestamp createdOn;



    private Long profileId;

    private Long userid;

    private int isPublic;
}
