package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class CandidateProfile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long candidateProfileId;

    @OneToOne
    @JoinColumn(name = "userId", nullable = false)
    private Registereduser registereduser;

    private String fullName;
    private String phoneNumber;
    private String resumeUrl;
    private String website;

    @Column(columnDefinition = "TEXT")
    private String skills;
    private String jobTitle;

    @Column(columnDefinition = "TEXT")
    private String jobCategories;

    @Column(columnDefinition = "TEXT")
    private String jobSubCategories;

    @Column(columnDefinition = "TEXT")
    private String jobSubSubCategories;

    private Long expectedSalary;
    private String salaryCurrency;

    private String yearsOfExperience;
    private String designation;
    private Long currentSalary;
    private String education;

    private String location;
    private String addressCountry;
    private String addressCity;
    private String district;
    private String addressLineOne;
    private String addressLineTwo;
    private String addressPincode;


    private String socialLinkedIn;
    private String socialFacebook;
    private String socialTwitter;
    private String socialGlassDoor;

    @Column(columnDefinition = "LONGTEXT")
    private String profilePictureURL;
    private String profilePictureKey;

    private Double candidateAddressMapLocationLattitude;
    private Double candidateAddressMapLocationLongtitude;

    @Column(columnDefinition = "TEXT")
    private String description;

    private String language;

    private String experience;

    @CreationTimestamp
    private Timestamp postedDate;
    @UpdateTimestamp
    private Timestamp updatedDate;
    private Boolean isActive = true;
}