package com.job.jobportal.controller;

import com.job.jobportal.dto.MasterDataDto;
import com.job.jobportal.dto.SEOPagesDTO;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.SEOPages;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.MasterDataService;
import com.job.jobportal.service.SEOPagesService;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController

public class SEOPagesController {

    @Autowired
    SEOPagesService seoPagesService;

    @Autowired
    MasterDataService masterDataService;

    @Autowired
    MasterDataRepository masterDataRepository;

    @Autowired
    MessageSource message;




    private static final Logger logger = LoggerFactory.getLogger(SEOPagesController.class);


    @PostMapping("/seopages")
    public ResponseEntity<?> addSeoPages(@RequestBody SEOPagesDTO seoPagesDTO) {
        try {
            SEOPages seoPages = seoPagesService.addSeoPages(seoPagesDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, seoPages, message.getMessage("msg.seopages_added_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/seopages/addthumbnail")
    public ResponseEntity<?> addThumbnail(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> url = seoPagesService.addThumbnail(file);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, url, message.getMessage("msg.thumbnail_added_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/seopages")
    public ResponseEntity<?> updateSeoPages(@RequestBody SEOPagesDTO seoPagesDTO) {
        try {
            SEOPages seoPages = seoPagesService.updateSeoPages(seoPagesDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, seoPages, message.getMessage("msg.seopages_updated_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/seopages/{seoPagesId}")
    public ResponseEntity<?> deleteSeoPages(@PathVariable("seoPagesId") Long seoPagesId) {
        try {
            seoPagesService.deleteSeoPages(seoPagesId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.seopages_deleted_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/seopages")
    public ResponseEntity<?> getSeoPages(@RequestParam(defaultValue = "1") int page,
                                         @RequestParam(defaultValue = "5") int size, @RequestParam(required = false) String search) {
        try {
            Map<String, Object> map = seoPagesService.getAllSeoPages(page, size, search);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, map, message.getMessage("msg.seopages_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/seopages/title/{seoPagesTitle}")
    public ResponseEntity<?> getSeoPagesDetails(@PathVariable("seoPagesTitle") String seoPagesTitle) {
        try {
            SEOPages seoPages = seoPagesService.getSeoPagesByURL(seoPagesTitle);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, seoPages, message.getMessage("msg.seopagesdetails_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/seopages/id/{seoPagesId}")
    public ResponseEntity<?> getSeoPagesDetails(@PathVariable("seoPagesId") Long seoPagesId) {
        try {
            SEOPages seoPages = seoPagesService.getSeoPagesById(seoPagesId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, seoPages, message.getMessage("msg.seopagesdetails_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/seopages/categories")
    public ResponseEntity<?> getSeoCategories() {
        try {
            List<MasterDataDto> categories = masterDataService.getMasterDataByComponentTypeId(ConstantsUtil.COMPONENT_TYPE_SEO_CATEGORIES);
            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            categories,
                            message.getMessage("msg.categories_fetched", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching SEO categories: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/seopages/subcategories/{categoryId}")
    public ResponseEntity<?> getSeoSubcategories(@PathVariable Integer categoryId) {
        try {
            List<MasterData> allSubcategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.COMPONENT_TYPE_SEO_SUBCATEGORIES);

            List<MasterDataDto> filteredSubcategories = allSubcategories.stream()
                .filter(data -> {
                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        String[] parts = value.split("\\|");
                        if (parts.length > 1) {
                            try {
                                int parentId = Integer.parseInt(parts[1]);
                                return parentId == categoryId;
                            } catch (NumberFormatException e) {
                                logger.error("Error parsing parent category ID: {}", e.getMessage());
                                return false;
                            }
                        }
                    }
                    return false;
                })
                .map(data -> {
                    MasterDataDto dto = new MasterDataDto();
                    dto.setId(data.getId());
                    dto.setMasterDataId(data.getMasterDataId());


                    String value = data.getValue();
                    if (value != null && value.contains("|")) {
                        dto.setValue(value.split("\\|")[0]);
                    } else {
                        dto.setValue(value);
                    }

                    return dto;
                })
                .collect(Collectors.toList());

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            filteredSubcategories,
                            message.getMessage("msg.seo_subcategories_fetched", null, LocaleContextHolder.getLocale())
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching subcategories for category ID {}: {}", categoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/seopages/subsubcategories/{subcategoryId}")
    public ResponseEntity<?> getSeoSubSubcategories(@PathVariable Integer subcategoryId) {
        try {
            logger.info("Fetching SEO sub-subcategories for subcategory ID: {}", subcategoryId);
            List<MasterDataDto> subSubcategories = masterDataService.getSeoSubSubcategories(subcategoryId);

            if (subSubcategories == null) {
                subSubcategories = new ArrayList<>();
            }

            logger.info("Found {} SEO sub-subcategories for subcategory ID: {}", subSubcategories.size(), subcategoryId);

            String responseMessage;
            try {
                responseMessage = message.getMessage("msg.seo_subsubcategories_fetched", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                responseMessage = "SEO sub-subcategories fetched successfully";
            }

            return new ResponseEntity<>(
                    new ApiResponse<>(
                            HttpStatus.OK,
                            true,
                            subSubcategories,
                            responseMessage
                    ),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error fetching SEO sub-subcategories for subcategory ID {}: {}", subcategoryId, e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/seopages/search")
    public ResponseEntity<?> getSeoPagesByParams(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String subcategory,
            @RequestParam(required = false) String subSubcategory,
            @RequestParam(required = false) String title) {
        try {
            logger.info("Controller received search request with parameters: category='{}', subcategory='{}', subSubcategory='{}', title='{}'",
                    category, subcategory, subSubcategory, title);

            Optional<SEOPages> exactMatch = seoPagesService.getSeoPageByExactUrl(
                    category, subcategory, subSubcategory, title);

            if (exactMatch.isPresent()) {
                logger.info("Found exact URL match for the provided parameters");
                SEOPages page = exactMatch.get();

                String responseMessage = message.getMessage(
                        "msg.seopagesdetails_get_success",
                        null,
                        "SEO page fetched successfully",
                        LocaleContextHolder.getLocale()
                );

                return new ResponseEntity<>(
                        new ApiResponse<>(
                                HttpStatus.OK,
                                true,
                                page,
                                responseMessage
                        ),
                        HttpStatus.OK
                );
            } else {
                logger.info("No exact URL match found, returning null");
                String responseMessage = message.getMessage(
                        "msg.no_seo_pages_found",
                        null,
                        "No SEO pages found",
                        LocaleContextHolder.getLocale()
                );

                return new ResponseEntity<>(
                        new ApiResponse<>(
                                HttpStatus.OK,
                                true,
                                null,
                                responseMessage
                        ),
                        HttpStatus.OK
                );
            }
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

}
