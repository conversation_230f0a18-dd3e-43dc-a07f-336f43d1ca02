package com.job.jobportal.dto;

import com.job.jobportal.validation.ValidEmail;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SubscriberDTO {

    @ValidEmail
    @NotNull(message = "Email is required")
    @NotEmpty(message = "Email cannot be empty")
    private String email;

    @NotNull(message = "Subscriber status is required")
    private Boolean isSubscriber;
}
