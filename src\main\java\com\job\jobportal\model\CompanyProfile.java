package com.job.jobportal.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.sql.Date;
import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class CompanyProfile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long companyProfileId;

    private String companyName;

    @Column(columnDefinition = "LONGTEXT")
    private String companyLogoURL;

    @Column(columnDefinition = "LONGTEXT")
    private String companyCoverURL;

    private String companyLogoKey;

    private String companyCoverKey;

    @Column(columnDefinition = "LONGTEXT")
    private String featureImageUrl;

    private String featureImageKey;

    @Column(columnDefinition = "LONGTEXT")
    private String videoUrl;

    private String videoKey;

    private String companyEmail;

    private String companyWebsite;

    private int companyTeamSize;

    @Temporal(TemporalType.DATE)
    private Date companyEstYear;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private JsonNode companyDescription;

    private String companySocialLinkedIn;

    private String companySocialFacebook;

    private String companySocialTwitter;

    private String companySocialGlassDoor;

    private String companyAddressCountry;

    private String companyPhoneNumber;

    @CreationTimestamp
    private Timestamp postedDate;

    @UpdateTimestamp
    private Timestamp updatedDate;

    private int companyAllowInSearch;

    private int companyNatureOfBusiness;

    private String companyAddressState;

    private String companyAddressCity;

    private String companyAddressDistrict;

    private String companyAddressLineOne;

    private String companyAddressLineTwo;

    private String companyAddressPincode;

    private Double companyAddressMapLocationLattitude;
    private Double companyAddressMapLocationLongtitude;

    private boolean companyIsActive;

    private boolean companyIsArchived;

    private String currentUserEmail;

    private String authorizedPersonFirstName;
    private String authorizedPersonLastName;
    private String authorizedPersonDesignation;
    private String companyRegistrationNumber;
    private boolean isAuthorizedPerson;

    @Transient
    private Long transientActiveJobCount;

    @Transient
    private Long activeJobCount;

    @Column(columnDefinition = "LONGTEXT")
    private String emailDomainHost;

    @Column(columnDefinition = "LONGTEXT")
    private String emailDomainUsername;

    @Column(columnDefinition = "LONGTEXT")
    private String emailDomainPassword;
}
