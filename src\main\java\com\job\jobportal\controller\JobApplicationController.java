package com.job.jobportal.controller;

import com.job.jobportal.dto.*;
import com.job.jobportal.model.ApplicationStatus;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.service.JobApplicationService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

@RestController
@RequestMapping("/api/job-applications")
public class JobApplicationController {

    @Autowired
    private JobApplicationService jobApplicationService;

    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(JobApplicationController.class);
    /*
    @PostMapping("/apply/{jobPostId}")
    public ResponseEntity<?> applyForJob(@PathVariable Long jobPostId,
                                         @RequestBody (required = false) JobApplicationRequest request
    ) {
        try {
            JobApplicationResponseDTO application = jobApplicationService.applyForJob(jobPostId,request);
            logger.info("Job application successful for jobPostId: {}", jobPostId);
            String successMessage = messageSource.getMessage("msg.job_application_submit_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.CREATED, true, application, successMessage), HttpStatus.CREATED);
        } catch (RuntimeException e) {
            logger.error("Error applying for job: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }
    */
    @PostMapping("/apply/{jobPostId}")
    public ResponseEntity<?> applyForJob(@PathVariable Long jobPostId,
                                         @RequestBody(required = false) JobApplicationRequest request) {
        try {
            JobApplicationFullResponseDTO response = jobApplicationService.applyForJob(jobPostId, request);
            logger.info("Job application successful for jobPostId: {}", jobPostId);
            String successMessage = messageSource.getMessage("msg.job_application_submit_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.CREATED, true, response, successMessage), HttpStatus.CREATED);
        } catch (RuntimeException e) {
            logger.error("Error applying for job: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/count/{jobPostId}")
    public ResponseEntity<?> getJobApplicationCount(@PathVariable Long jobPostId) {
        try {
            long count = jobApplicationService.getJobApplicationCount(jobPostId);
            logger.info("Job application count retrieved for jobPostId: {}", jobPostId);
            String successMessage = messageSource.getMessage("msg.job_application_count_fetch_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, count, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error retrieving job application count: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }
/*
    @PutMapping("/update-status/{applicationId}")
    public ResponseEntity<?> updateApplicationStatus(
            @PathVariable Long applicationId,
            @RequestParam(value = "statusId", required = false) Integer statusIdParam,
            @RequestParam(value = "status", required = false) Integer statusParam) {
        // Use statusId if provided, otherwise use status parameter
        Integer statusId = statusIdParam != null ? statusIdParam : statusParam;

        if (statusId == null) {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null,
                    "Status ID is required"), HttpStatus.BAD_REQUEST);
        }
        try {
            JobApplicationDTO updatedApplication = jobApplicationService.updateApplicationStatus(applicationId, statusId);
            logger.info("Application status updated for applicationId: {}", applicationId);
            String successMessage = messageSource.getMessage("msg.job_application_status_update_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, updatedApplication, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error updating application status: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }*/

    @GetMapping
    public ResponseEntity<?> getJobApplications(
            @RequestParam(required = false) String jobTitle,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) Integer statusId,
            @RequestParam(required = false) Integer timeFilterId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "appliedDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir
    ) {
        try {
            Page<? extends JobApplicationDTO> applications = jobApplicationService
                    .getJobApplications(jobTitle, companyName, statusId, timeFilterId, page, size, sortBy, sortDir);
            String successMessage = messageSource.getMessage("msg.job_application_retrieve_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, applications, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error retrieving job applications: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/update-status/{applicationId}/status/{statusId}")
    public ResponseEntity<?> updateApplicationStatusPath(
            @PathVariable Long applicationId,
            @PathVariable Integer statusId) {
        try {
            JobApplicationDTO updatedApplication = jobApplicationService.updateApplicationStatus(applicationId, statusId);
            logger.info("Application status updated for applicationId: {}", applicationId);
            String successMessage = messageSource.getMessage("msg.job_application_status_update_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, updatedApplication, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error updating application status: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/has-applied/{jobPostId}")
    public ResponseEntity<?> hasCandidateApplied(@PathVariable Long jobPostId) {
        try {
            boolean hasApplied = jobApplicationService.hasCandidateApplied(jobPostId);
            logger.info("Application status checked for candidate on jobPostId: {}", jobPostId);
            String successMessage = messageSource.getMessage("msg.job_application_has_applied_check_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, hasApplied, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error checking application status: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/jobs/applicants")
    public ResponseEntity<?> getJobApplicants(
            @RequestParam(required = false) Long jobId,
            @RequestParam(required = false) Integer statusId,
            @RequestParam(required = false) Integer timeFilterId
    ) {
        try {
            logger.info("Processing request with parameters - jobId: {}, statusId: {}, timeFilterId: {}", jobId, statusId, timeFilterId);

            JobApplicantsResponseDTO response = jobApplicationService.getJobApplicants(jobId, statusId, timeFilterId);

            String successMessage;
            if (jobId == null) {
                logger.info("Retrieved applicants across all jobs with filters - statusId: {}, timeFilterId: {}", statusId, timeFilterId);
                successMessage = messageSource.getMessage("msg.jobs_with_applicants_retrieve_success", null, LocaleContextHolder.getLocale());
            } else {
                logger.info("Retrieved applicants for job ID: {} with filters - statusId: {}, timeFilterId: {}", jobId, statusId, timeFilterId);
                successMessage = messageSource.getMessage("msg.job_applicants_retrieve_success", null, LocaleContextHolder.getLocale());
            }

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, response, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error retrieving job applicants: {}", e.getMessage());
            if (e.getMessage().contains("company profile")) {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.FORBIDDEN, false, null, e.getMessage()), HttpStatus.FORBIDDEN);
            } else if (e.getMessage().contains("not found") || e.getMessage().contains("not owned")) {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.NOT_FOUND, false, null, e.getMessage()), HttpStatus.NOT_FOUND);
            } else {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
            }
        }
    }

    @PostMapping("/send-bulk-email")
    public ResponseEntity<?> sendBulkEmail(@Valid @RequestBody BulkEmailRequestDTO bulkEmailRequestDTO) {
        try {
            jobApplicationService.sendBulkEmail(bulkEmailRequestDTO);
            logger.info("Bulk email sent to {} applicants", bulkEmailRequestDTO.getApplicantIds().size());
            String successMessage = messageSource.getMessage("msg.bulk_email_sent_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, successMessage), HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Error sending bulk email: {}", e.getMessage());
            if (e.getMessage().contains("company profile") || e.getMessage().contains("permission")) {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.FORBIDDEN, false, null, e.getMessage()), HttpStatus.FORBIDDEN);
            } else if (e.getMessage().contains("not found")) {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.NOT_FOUND, false, null, e.getMessage()), HttpStatus.NOT_FOUND);
            } else {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()), HttpStatus.BAD_REQUEST);
            }
        }
    }
}