package com.job.jobportal.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ResumeDTO {
    private Long resumeId;
    @NotBlank(message = "msg.validation.title_required")
    private String title;
    private String description;
    private Boolean isActive;
    private List<EducationDTO> educationEntries;
    private List<WorkExperienceDTO> workExperiences;
    private List<AwardDTO> awards;
}