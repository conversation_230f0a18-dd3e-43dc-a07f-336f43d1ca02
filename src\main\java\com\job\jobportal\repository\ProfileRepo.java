package com.job.jobportal.repository;

import com.job.jobportal.model.Profile;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
@Transactional
public interface ProfileRepo extends JpaRepository<Profile, Long>, ProfileStoredProcedure {


    @Modifying
    @Query("UPDATE Profile u SET u.profileUrl =:profileUrl  WHERE u.profileId =:profileId")
    int updateProfileUrl(@Param("profileUrl") String profileUrl, @Param("profileId") Long profileId);


}
