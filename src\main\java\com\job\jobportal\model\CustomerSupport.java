package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class CustomerSupport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long customerSupportId;

    private String ticketNumber;

    private int issueCategory;

    @Column(columnDefinition = "LONGTEXT")
    private String issueDescription;

    private int issueStatus;

    private Long userid;

    private Timestamp issueTime;

}
