package com.job.jobportal.service;

import com.job.jobportal.model.CompanyProfile;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Properties;

@Service
public class EmailService {

    private final JavaMailSenderImpl mailSender;
    private final Environment env;
    private final CompanyProfileService companyProfileService;

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Autowired
    public EmailService(JavaMailSenderImpl mailSender, Environment env, @Lazy CompanyProfileService companyProfileService) {
        this.mailSender = mailSender;
        this.env = env;
        this.companyProfileService = companyProfileService;
    }

    @Async
    public void sendEmail(String from, String to, String subject, String body) {
        configureMailSender();

        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(from);
        message.setTo(to);
        message.setSubject(subject);
        message.setText(body);

        mailSender.send(message);
    }

    @Async
    public void sendHtmlEmail(String from, String to, String subject, String htmlContent) {
        try {
            configureMailSender();

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);

            mailSender.send(mimeMessage);
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send HTML email: " + e.getMessage(), e);
        }
    }

    private void configureMailSender() {
        String provider = env.getProperty("email.provider", "gmail");
        Properties props = new Properties();
        if ("domain".equalsIgnoreCase(provider)) {
            mailSender.setHost(env.getProperty("email.domain.host"));
            mailSender.setPort(Integer.parseInt(env.getProperty("email.domain.port", "465")));
            mailSender.setUsername(env.getProperty("email.domain.username"));
            mailSender.setPassword(env.getProperty("email.domain.password"));
            mailSender.setProtocol("smtps");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.socketFactory.port", "465");
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("smtp.socketFactory.fallback", "false");
        } else {
            mailSender.setHost(env.getProperty("spring.mail.host"));
            mailSender.setPort(Integer.parseInt(env.getProperty("email.mail.port", "587")));
            mailSender.setUsername(env.getProperty("spring.mail.username"));
            mailSender.setPassword(env.getProperty("spring.mail.password"));
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
        }
        mailSender.setJavaMailProperties(props);
    }

    private boolean configureMailSenderWithCompanyCredentials(CompanyProfile companyProfile) {
        try {
            if (companyProfile == null ||
                    companyProfile.getEmailDomainHost() == null ||
                    companyProfile.getEmailDomainUsername() == null ||
                    companyProfile.getEmailDomainPassword() == null) {
                logger.warn("Company profile email settings are incomplete, using default settings");
                return false;
            }

            Properties props = new Properties();
            mailSender.setHost(companyProfile.getEmailDomainHost());
            mailSender.setPort(465);
            mailSender.setUsername(companyProfile.getEmailDomainUsername());

            String decryptedPassword = companyProfileService.getDecryptedEmailDomainPassword(companyProfile);
            if (decryptedPassword == null) {
                logger.warn("Failed to decrypt company email password, using default settings");
                return false;
            }

            mailSender.setPassword(decryptedPassword);
            mailSender.setProtocol("smtps");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.socketFactory.port", "465");
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("smtp.socketFactory.fallback", "false");
            mailSender.setJavaMailProperties(props);

            logger.info("Configured mail sender with company-specific credentials for {}", companyProfile.getCompanyName());
            return true;
        } catch (Exception e) {
            logger.error("Error configuring mail sender with company credentials: {}", e.getMessage());
            return false;
        }
    }

    @Async
    public void sendBulkEmail(String from, String to, String subject, String body, CompanyProfile companyProfile) {
        boolean useCompanySettings = false;
        String actualFrom = from;

        if (companyProfile != null) {
            useCompanySettings = configureMailSenderWithCompanyCredentials(companyProfile);
            if (useCompanySettings && companyProfile.getEmailDomainUsername() != null) {
                actualFrom = companyProfile.getEmailDomainUsername();
                logger.info("Using company email {} as from address instead of {}", actualFrom, from);
            }
        }

        if (!useCompanySettings) {
            configureMailSender();
        }

        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(actualFrom);
        message.setTo(to);
        message.setSubject(subject);
        message.setText(body);

        mailSender.send(message);
        logger.info("Sent bulk email to {} using {} settings", to, useCompanySettings ? "company" : "default");
    }

    @Async
    public void sendBulkHtmlEmail(String from, String to, String subject, String htmlContent, CompanyProfile companyProfile) {
        try {
            boolean useCompanySettings = false;
            String actualFrom = from;

            if (companyProfile != null) {
                useCompanySettings = configureMailSenderWithCompanyCredentials(companyProfile);
                if (useCompanySettings && companyProfile.getEmailDomainUsername() != null) {
                    actualFrom = companyProfile.getEmailDomainUsername();
                    logger.info("Using company email {} as from address instead of {}", actualFrom, from);
                }
            }

            if (!useCompanySettings) {
                configureMailSender();
            }

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(actualFrom);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);

            mailSender.send(mimeMessage);
            logger.info("Sent bulk HTML email to {} using {} settings", to, useCompanySettings ? "company" : "default");
        } catch (MessagingException e) {
            logger.error("Failed to send bulk HTML email: {}", e.getMessage());
            throw new RuntimeException("Failed to send HTML email: " + e.getMessage(), e);
        }
    }
}