package com.job.jobportal.controller;

import com.job.jobportal.dto.CompanyProfileDTO;
import com.job.jobportal.dto.CompanyProfileResponse;
import com.job.jobportal.dto.EmailSettingsDTO;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.CompanyProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/company-profile")
public class CompanyProfileController {

    @Autowired
    private CompanyProfileService companyProfileService;

    @Autowired
    private MessageSource message;

    private static final Logger logger = LoggerFactory.getLogger(CompanyProfileController.class);

    @PostMapping("/add")
    public ResponseEntity<?> addCompanyProfile(@RequestBody CompanyProfileDTO companyProfileDTO) {
        try {
            CompanyProfileResponse companyProfileResponse = companyProfileService.addCompanyProfile(companyProfileDTO);

            String successMessage;
            try {
                successMessage = message.getMessage("msg.company_profile_created", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                successMessage = "Company profile added successfully";
            }

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, companyProfileResponse, successMessage), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/update")
    public ResponseEntity<?> updateCompanyProfile(@RequestBody CompanyProfileDTO companyProfileDTO) {
        try {
            CompanyProfile companyProfile = companyProfileService.updateCompanyProfile(companyProfileDTO);

            String successMessage;
            try {
                successMessage = message.getMessage("msg.company_profile_updated", null, LocaleContextHolder.getLocale());
            } catch (Exception ex) {
                successMessage = "Company profile updated successfully";
            }

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, companyProfile, successMessage), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/get")
    public ResponseEntity<?> getCompanyProfile() {
        try {
            CompanyProfile companyProfile = companyProfileService.getCompanyProfile();
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    companyProfile,
                    message.getMessage("msg.company_profile_fetched", null, LocaleContextHolder.getLocale())
                ),
                HttpStatus.OK
            );
        } catch (RuntimeException e) {
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    new CompanyProfile(),
                    message.getMessage("msg.no_company_profile", null, LocaleContextHolder.getLocale())
                ),
                HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    e.getMessage()
                ),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @GetMapping("/all")
    public ResponseEntity<?> getCompanies(
            @RequestParam(required = false) Boolean isArchived,
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) String companyAddressCity,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "postedDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {

        try {
            Page<CompanyProfile> companies = companyProfileService.getFilteredCompanies(
                    isArchived, isActive, companyName, companyAddressCity, sortBy, sortDirection, page, size);

            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, companies, "msg.company_profile_fetched"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()));
        }
    }

    @GetMapping("/get/{companyProfileId}")
    public ResponseEntity<?> getCompanyProfileById(@PathVariable Long companyProfileId) {
        try {
            CompanyProfile companyProfile = companyProfileService.getPublicCompanyProfileById(companyProfileId);
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    companyProfile,
                    message.getMessage("msg.company_profile_fetched", null, LocaleContextHolder.getLocale())
                ),
                HttpStatus.OK
            );
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PutMapping("/archive/{companyProfileId}")
    public ResponseEntity<?> updateCompanyArchiveStatus(@PathVariable Long companyProfileId, @RequestParam boolean isArchived) {
        try {
            CompanyProfileDTO companyProfileDTO=companyProfileService.updateCompanyArchiveStatus(companyProfileId, isArchived);
            String messageKey = isArchived ? "msg.company_profile_archived" : "msg.company_profile_restored";
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, companyProfileDTO, message.getMessage(messageKey, null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/activate/{companyProfileId}")
    public ResponseEntity<?> updateCompanyActiveStatus(@PathVariable Long companyProfileId, @RequestParam boolean isActive) {
        try {
            CompanyProfileDTO companyProfileDTO=companyProfileService.updateCompanyActiveStatus(companyProfileId, isActive);
            String messageKey = isActive ? "msg.company_profile_activated" : "msg.company_profile_deactivated";
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, companyProfileDTO, message.getMessage(messageKey, null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/delete/{companyProfileId}")
    public ResponseEntity<?> deleteCompany(@PathVariable Long companyProfileId) {
        try {
            companyProfileService.deleteCompany(companyProfileId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.company_profile_deleted", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/email-settings")
    public ResponseEntity<?> getCurrentUserEmailSettings() {
        try {
            CompanyProfile companyProfile = companyProfileService.getCompanyProfile();

            if (companyProfile == null || companyProfile.getCompanyProfileId() == null) {
                return new ResponseEntity<>(
                        new ApiResponse<>(HttpStatus.NOT_FOUND, false, null, "No company profile found for current user"),
                        HttpStatus.NOT_FOUND
                );
            }

            EmailSettingsDTO emailSettingsDTO = new EmailSettingsDTO();
            emailSettingsDTO.setEmail(companyProfile.getEmailDomainUsername());
            emailSettingsDTO.setSmtpDetails(companyProfile.getEmailDomainHost());
            // We don't return the password for security reasons
            emailSettingsDTO.setPassword("");

            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, emailSettingsDTO, "Email settings retrieved successfully"),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error retrieving email settings for current user: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PutMapping("/email-settings")
    public ResponseEntity<?> updateCurrentUserEmailSettings(@RequestBody EmailSettingsDTO emailSettingsDTO) {
        try {
            CompanyProfile companyProfile = companyProfileService.getCompanyProfile();

            if (companyProfile == null || companyProfile.getCompanyProfileId() == null) {
                return new ResponseEntity<>(
                        new ApiResponse<>(HttpStatus.NOT_FOUND, false, null, "No company profile found for current user"),
                        HttpStatus.NOT_FOUND
                );
            }

            String email = emailSettingsDTO.getEmail();
            String password = emailSettingsDTO.getPassword();
            String smtpDetails = emailSettingsDTO.getSmtpDetails();

            if (email == null || password == null || smtpDetails == null) {
                return new ResponseEntity<>(
                        new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, "All email settings are required"),
                        HttpStatus.BAD_REQUEST
                );
            }

            CompanyProfile updated = companyProfileService.updateEmailDomainSettings(
                    companyProfile.getCompanyProfileId(), smtpDetails, email, password
            );

            Map<String, String> responseSettings = new HashMap<>();
            responseSettings.put("email", updated.getEmailDomainUsername());
            responseSettings.put("smtpDetails", updated.getEmailDomainHost());

            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, responseSettings, "Email settings updated successfully"),
                    HttpStatus.OK
            );
        } catch (Exception e) {
            logger.error("Error updating email settings for current user: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}