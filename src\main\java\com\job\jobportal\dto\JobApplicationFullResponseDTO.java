package com.job.jobportal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.model.JobApplication;
import com.job.jobportal.model.JobPost;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
public class JobApplicationFullResponseDTO {
    private Long applicationId;
    private Long candidateProfileId;
    private Long companyProfileId;
    private Long jobPostId;
    private Integer statusId;
    private String statusValue;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp appliedDate;

    private Long companyActiveJobCount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp updatedDate;
    private String jobTitle;
    private String jobDescription;
    private String location;
    private Integer locationId;
    private String locationName;
    private Long minSalary;
    private Long maxSalary;
    private String jobType;
    private Integer jobTypeId;
    private String jobTypeName;
    private String careerLevel;
    private Integer careerLevelId;
    private String careerLevelName;
    private String experience;
    private Integer experienceId;
    private String experienceName;
    private String industry;
    private Integer industryId;
    private String industryName;
    private String qualification;
    private Integer qualificationId;
    private String qualificationName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp postedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp applicationDeadlineDate;
    private String statusJob;
    private String companyName;
    private String companyLogoUrl;
    private String companyEmail;
    private String companyPhoneNumber;
    private String companyWebsite;
    private String companyAddressCity;
    private String companyAddressCountry;
    private String companyAddressComplete;
    private String state;
    private String pincode;
    private String jobCategory;
    private Integer jobCategoryId;
    private String jobCategoryName;
    private String jobSubCategory;
    private Integer jobSubCategoryId;
    private String jobSubCategoryName;
    private String jobSubSubCategory;
    private Integer jobSubSubCategoryId;
    private String jobSubSubCategoryName;
    private String completeAddress;
    private String salaryCurrency;
    private Integer salaryCurrencyId;
    private String salaryCurrencyName;
    private String payType;
    private Integer payTypeId;
    private String payTypeName;
    private String resumeUrl;
    private String resumeKey;

    private CandidateProfileDTO candidateProfileDTO;
    private String jwtToken;
    private String refreshToken;

    public JobApplicationFullResponseDTO(JobApplication jobApplication, JobPost jobPost, JobApplicationResponse response) {
        this.applicationId = jobApplication.getApplicationId();
        this.candidateProfileId = jobApplication.getCandidateProfile().getCandidateProfileId();
        this.companyProfileId = jobApplication.getCompanyProfile().getCompanyProfileId();
        this.jobPostId = jobApplication.getJobPost().getJobId();
        this.statusId = jobApplication.getStatusId();
        this.appliedDate = jobApplication.getAppliedDate();
        this.updatedDate = jobApplication.getUpdatedDate();
        this.resumeUrl = jobApplication.getResumeUrl();
        this.resumeKey = jobApplication.getResumeKey();
        this.jobTitle = jobPost.getJobTitle();
        this.jobDescription = jobPost.getJobDescription() != null ? jobPost.getJobDescription().toString() : null;
        this.location = jobPost.getLocation();
        this.minSalary = jobPost.getMinSalary();
        this.maxSalary = jobPost.getMaxSalary();
        this.salaryCurrency = jobPost.getSalaryCurrency();
        this.payType = jobPost.getPayType();

        this.locationId = parseIntOrNull(jobPost.getLocation());
        this.jobType = jobPost.getJobType();
        this.jobTypeId = parseIntOrNull(jobPost.getJobType());
        this.careerLevel = jobPost.getCareerLevel();
        this.careerLevelId = parseIntOrNull(jobPost.getCareerLevel());
        this.experience = jobPost.getExperience();
        this.experienceId = parseIntOrNull(jobPost.getExperience());
        this.industry = jobPost.getIndustry();
        this.industryId = parseIntOrNull(jobPost.getIndustry());
        this.qualification = jobPost.getQualification();
        this.qualificationId = parseIntOrNull(jobPost.getQualification());
        this.jobCategory = jobPost.getJobCategory();
        this.jobCategoryId = parseIntOrNull(jobPost.getJobCategory());
        this.jobSubCategory = jobPost.getJobSubCategory();
        this.jobSubCategoryId = parseIntOrNull(jobPost.getJobSubCategory());
        this.jobSubSubCategory = jobPost.getJobSubSubCategory();
        this.jobSubSubCategoryId = parseIntOrNull(jobPost.getJobSubSubCategory());
        this.salaryCurrencyId = parseIntOrNull(jobPost.getSalaryCurrency());
        this.payTypeId = parseIntOrNull(jobPost.getPayType());

        this.postedDate = jobPost.getPostedDate();
        this.applicationDeadlineDate = jobPost.getApplicationDeadlineDate();
        this.statusJob = jobPost.getStatus().toString();
        this.state = jobPost.getState();
        this.pincode = jobPost.getPincode();
        this.completeAddress = jobPost.getCompleteAddress();

        if (response.getJobCategoryName() != null) {
            this.jobCategoryName = response.getJobCategoryName();
        }

        if (response.getJobSubCategoryName() != null) {
            this.jobSubCategoryName = response.getJobSubCategoryName();
        }

        if (response.getJobSubSubCategoryName() != null) {
            this.jobSubSubCategoryName = response.getJobSubSubCategoryName();
        }

        CompanyProfile company = jobPost.getCompanyProfile();
        this.companyName = company.getCompanyName();
        this.companyLogoUrl = company.getCompanyLogoURL();
        this.companyEmail = company.getCompanyEmail();
        this.companyPhoneNumber = company.getCompanyPhoneNumber();
        this.companyWebsite = company.getCompanyWebsite();
        this.companyAddressCity = company.getCompanyAddressCity();
        this.companyAddressCountry = company.getCompanyAddressCountry();
        String addressLineTwo = company.getCompanyAddressLineTwo();
        this.companyAddressComplete = company.getCompanyAddressLineOne() + (addressLineTwo != null ? ", " + addressLineTwo : "");

        this.candidateProfileDTO = response.getCandidateProfileDTO();
        this.jwtToken = response.getJwtToken();
        this.refreshToken = response.getRefreshToken();
    }

    private Integer parseIntOrNull(String value) {
        if (value != null && !value.isEmpty()) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.error("Failed to parse integer value: " + value);
            }
        }
        return null;
    }
}