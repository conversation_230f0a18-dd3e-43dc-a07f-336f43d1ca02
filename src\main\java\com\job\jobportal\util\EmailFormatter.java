package com.job.jobportal.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class EmailFormatter {
    private static final Logger logger = LoggerFactory.getLogger(EmailFormatter.class);
    private static final Pattern URL_PATTERN = Pattern.compile(
            "\\b(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]");

    @Value("${app.base-url:https://www.groglojobs.co.uk}")
    private String baseUrl;
    @Value("${social.facebook.url:https://www.facebook.com/groglojobs}")
    private String facebookUrl;
    @Value("${social.linkedin.url:https://www.linkedin.com/company/groglojobs}")
    private String linkedinUrl;
    @Value("${social.twitter.url:https://twitter.com/groglojobs}")
    private String twitterUrl;
    @Value("${social.icon.facebook:https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png}")
    private String facebookIcon;
    @Value("${social.icon.linkedin:https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png}")
    private String linkedinIcon;
    @Value("${social.icon.twitter:https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png}")
    private String twitterIcon;

    public String convertToHtml(String plainText) {
        if (plainText == null) {
            return "";
        }
        String html = plainText.replace("\n", "<br/>");
        Matcher matcher = URL_PATTERN.matcher(html);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            String url = matcher.group();
            String replacement = "<a href=\"" + url + "\" style=\"color: #007bff; text-decoration: underline;\">Click Here</a>";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public String addSignature(String htmlContent) {
        StringBuilder signature = new StringBuilder();
        signature.append("<br/><br/>");
        signature.append("Regards,<br/>");
        signature.append("GroGlo Jobs Team<br/><br/>");

        signature.append("<div style=\"margin-top: 10px;\">");
        signature.append("<a href=\"").append(facebookUrl).append("\" style=\"margin-right: 10px; text-decoration: none;\">");
        signature.append("<img src=\"").append(facebookIcon).append("\" alt=\"Facebook\" style=\"width: 24px; height: 24px;\"/>");
        signature.append("</a>");
        signature.append("<a href=\"").append(linkedinUrl).append("\" style=\"margin-right: 10px; text-decoration: none;\">");
        signature.append("<img src=\"").append(linkedinIcon).append("\" alt=\"LinkedIn\" style=\"width: 24px; height: 24px;\"/>");
        signature.append("</a>");
        signature.append("<a href=\"").append(twitterUrl).append("\" style=\"margin-right: 10px; text-decoration: none;\">");
        signature.append("<img src=\"").append(twitterIcon).append("\" alt=\"Twitter\" style=\"width: 24px; height: 24px;\"/>");
        signature.append("</a>");
        signature.append("</div>");

        signature.append("<div style=\"margin-top: 10px; font-size: 12px; color: #666;\">");
        signature.append("<a href=\"").append(baseUrl).append("\" style=\"color: #007bff; text-decoration: underline;\">");
        String domain = baseUrl.replace("https://", "").replace("http://", "").replace("/", "");
        signature.append(domain).append("</a>");
        signature.append("</div>");

        return htmlContent + signature.toString();
    }

    public String formatEmailContent(String plainText) {
        String htmlContent = convertToHtml(plainText);
        return addSignature(htmlContent);
    }

    public String formatHtmlEmailContent(String htmlContent) {
        return addSignature(htmlContent);
    }
}
