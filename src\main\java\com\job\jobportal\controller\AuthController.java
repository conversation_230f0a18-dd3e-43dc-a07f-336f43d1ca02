package com.job.jobportal.controller;

import com.job.jobportal.dto.AdminLoginDTO;
import com.job.jobportal.dto.UserDTO;
import com.job.jobportal.model.OtpRequest;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.response.*;
import com.job.jobportal.security.AuthProvider;
import com.job.jobportal.security.PasswordResetToken;
import com.job.jobportal.security.TokenProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.service.*;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/auth")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    EmailService emailService;
    @Autowired
    MessageSource message;


    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private SocialSignInService socialSignInService;

    @Autowired
    private UserService userService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private TokenProvider tokenProvider;
    @Value("${application.baseUrl}")
    private String baseUrl;
    @Value("${application.baseFrontendUrl}")
    private String baseFrontendUrl;

    @Value("${application.email}")
    private String email;

    @Value("${email.domain.from}")
    private String domainEmail;

    @Value("${email.provider}")
    private String emailProvider;

    @Autowired
    SubscriptionRepo subscriptionRepo;

    @Autowired
    private StripeService stripeService;

    @Autowired
    EmailOtpService otpService;

    @Autowired
    LoginTrackingService loginTrackingService;


    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            Authentication authentication = null;
            String tokenValue = "";
            Optional<Subscription> subscription = null;
            boolean status = otpService.verifyOTP(loginRequest.getEmail(), loginRequest.getOtp());
            Optional<Registereduser> registereduserOptional = userService.findByEmail(loginRequest.getEmail());
            Registereduser registereduser;

            if (status) {
                if (registereduserOptional.isPresent()) {
                    Registereduser existingUser = registereduserOptional.get();

                    boolean roleMatch = existingUser.getRoles().stream()
                            .anyMatch(role -> role.getRolename().equalsIgnoreCase(loginRequest.getRole()));

                    if (!roleMatch) {
                        return new ResponseEntity<>(
                                new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null,
                                        message.getMessage("msg.email_registered_as_" + loginRequest.getRole().toLowerCase(), null, LocaleContextHolder.getLocale())),
                                HttpStatus.BAD_REQUEST
                        );
                    }
                }

                if (!registereduserOptional.isPresent()) {

                    String token = UUID.randomUUID().toString();
                    UserDTO user = new UserDTO();
                    //   user.setFirstName(signUpRequest.getFirstName());
                    //   user.setLastName(signUpRequest.getLastName());

                    user.setUserName(loginRequest.getEmail());
                    user.setEmailId(loginRequest.getEmail());
                    user.setAuthType(AuthProvider.otp);
                    user.setRoleName(loginRequest.getRole());

                    //  user.setPassword(passwordEncoder.encode(signUpRequest.getPassword()));
                    // user.setMobileNo(signUpRequest.getMobileNo());
                    user.setRefreshToken(token);
                    user.setHasPassword(0);
                    Registereduser result = userService.saveUser(user);

                    userService.createPasswordResetTokenForUser(result.getUserid(), token, true);
                    registereduser = result;

                    if ("RECRUITER".equalsIgnoreCase(loginRequest.getRole())) {
                        stripeService.checkAndActivateTrialForRecruiter(registereduser);
                    }

                    //login
                    subscription = subscriptionRepo.findByRegistereduser(registereduser);

                    UserPrincipal userPrincipal = UserPrincipal.create(registereduser, subscription.orElse(null));


                    tokenValue = tokenProvider.createToken(userPrincipal);

                    if (loginRequest.getRole().equalsIgnoreCase(tokenProvider.getRoleFromToken(tokenValue))) {
                        loginTrackingService.checkFirstTimeEmployerLogin(registereduser);

                        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, new AuthResponse(tokenValue, registereduser.getRefreshToken()),
                                message.getMessage("msg.login_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
                    } else {
                        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.FORBIDDEN, false, null,
                                message.getMessage("msg.role_mismatch", null, LocaleContextHolder.getLocale())), HttpStatus.FORBIDDEN);
                    }


                } else {
                    registereduser = registereduserOptional.get();

                    if ("RECRUITER".equalsIgnoreCase(loginRequest.getRole())) {
                        stripeService.checkAndActivateTrialForRecruiter(registereduser);
                    }

                    subscription = subscriptionRepo.findByRegistereduser(registereduser);
                    UserPrincipal userPrincipal = UserPrincipal.create(registereduser, subscription.orElse(null));


                    tokenValue = tokenProvider.createToken(userPrincipal);
                    String result = userService.validatePasswordResetToken(registereduser.getRefreshToken());
                    if (result == null) {
                        userService.updateRefreshToken(registereduser.getRefreshToken(), registereduser.getUserid());
                    } else {
                        String token = UUID.randomUUID().toString();
                        userService.createPasswordResetTokenForUser(registereduser.getUserid(), token, true);
                        userService.updateRefreshToken(token, registereduser.getUserid());
                        registereduser.setRefreshToken(token);

                    }

                    loginTrackingService.checkFirstTimeEmployerLogin(registereduser);

                    return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, new AuthResponse(tokenValue, registereduser.getRefreshToken()),
                            message.getMessage("msg.login_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
                }


            } else {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.ACCEPTED, true, null,
                        "msg.otp_verify_failed"), HttpStatus.ACCEPTED);
            }


        } catch (BadCredentialsException e) {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, "msg.login_failed"), HttpStatus.UNAUTHORIZED);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.UNAUTHORIZED);

        }


    }

    @PostMapping("/admin/login")
    public ResponseEntity<?> adminAuthenticateUser(@Valid @RequestBody AdminLoginDTO loginRequest) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getEmail(),
                            loginRequest.getPassword()
                    )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            String token = tokenProvider.createToken(authentication);
            Registereduser registereduser = userService.findByEmail(loginRequest.getEmail()).get();

            if (registereduser.getAccountDetails().getIsActive() == ConstantsUtil.USER_INACTIVE) {
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, message.getMessage("msg.user_inactive", null, LocaleContextHolder.getLocale())), HttpStatus.UNAUTHORIZED);

            } else {
                // this is invalid token
                String result = userService.validatePasswordResetToken(registereduser.getRefreshToken());
                /*
                if (result != null) {
                    userService.updateRefreshToken(registereduser.getRefreshToken(), registereduser.getUserid());
                }*/
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, new AuthResponse(token, registereduser.getRefreshToken()),
                        message.getMessage("msg.login_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
            }
        } catch (BadCredentialsException e) {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, "msg.login_failed"), HttpStatus.UNAUTHORIZED);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.UNAUTHORIZED);

        }


    }


    @PostMapping("/otp")
    public ResponseEntity<?> generateOTP(@RequestBody OtpRequest otpRequest) {
        try {
            Optional<Registereduser> existingUserOpt = userService.findByEmail(otpRequest.getEmail());

            if (existingUserOpt.isPresent()) {
                Registereduser existingUser = existingUserOpt.get();

                String requestedRole = otpRequest.getRole();
                if (requestedRole != null && !requestedRole.isEmpty()) {
                    boolean roleMatch = existingUser.getRoles().stream()
                            .anyMatch(role -> role.getRolename().equalsIgnoreCase(requestedRole));

                    if (!roleMatch) {
                        String errorMsg;
                        try {
                            errorMsg = message.getMessage("msg.email_registered_under_different_role", null, LocaleContextHolder.getLocale());
                        } catch (Exception ex) {
                            errorMsg = "This Email ID is already registered under a different role; please use the correct login.";
                        }
                        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, errorMsg), HttpStatus.BAD_REQUEST);
                    }
                }
            }

            String otp = otpService.generateRandomOTP(otpRequest.getEmail());

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    message.getMessage("msg.otp_sent_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, e.getMessage()), HttpStatus.UNAUTHORIZED);

        }
    }

    @PostMapping("/resend/otp")
    public ResponseEntity<?> reSendOTP(@RequestBody OtpRequest otpRequest) {
        try {
            Optional<Registereduser> existingUserOpt = userService.findByEmail(otpRequest.getEmail());

            if (existingUserOpt.isPresent()) {
                Registereduser existingUser = existingUserOpt.get();

                String requestedRole = otpRequest.getRole();
                if (requestedRole != null && !requestedRole.isEmpty()) {
                    boolean roleMatch = existingUser.getRoles().stream()
                            .anyMatch(role -> role.getRolename().equalsIgnoreCase(requestedRole));

                    if (!roleMatch) {
                        String errorMsg;
                        try {
                            errorMsg = message.getMessage("msg.email_registered_under_different_role", null, LocaleContextHolder.getLocale());
                        } catch (Exception ex) {
                            errorMsg = "This Email ID is already registered under a different role; please use the correct login.";
                        }
                        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, errorMsg), HttpStatus.BAD_REQUEST);
                    }
                }
            }

            String otp = otpService.reSendOTP(otpRequest.getEmail());

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    message.getMessage("msg.otp_resend_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, e.getMessage()), HttpStatus.UNAUTHORIZED);

        }
    }

    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignUpRequest signUpRequest) {

        try {
            // Creating user's account

            String token = UUID.randomUUID().toString();
            UserDTO user = new UserDTO();
            user.setFirstName(signUpRequest.getFirstName());
            user.setLastName(signUpRequest.getLastName());
            user.setUserName(signUpRequest.getEmail());
            user.setEmailId(signUpRequest.getEmail());
            user.setAuthType(AuthProvider.local);
            user.setRoleName(signUpRequest.getRoleName());
            user.setPassword(passwordEncoder.encode(signUpRequest.getPassword()));
            user.setMobileNo(signUpRequest.getMobileNo());
            user.setRefreshToken(token);
            user.setHasPassword(1);
            Registereduser result = userService.saveUser(user);

            userService.createPasswordResetTokenForUser(result.getUserid(), token, true);

            if ("RECRUITER".equalsIgnoreCase(signUpRequest.getRoleName())) {
                stripeService.checkAndActivateTrialForRecruiter(result);
            }

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }


    @PostMapping("/social/signup")
    public ResponseEntity<?> socialRegisterUser(@RequestBody SocialSignUpRequest signUpRequest) {

        try {
            Map<String, Object> attributes = new HashMap<>();
            Authentication authentication = null;
            String tokenValue = "";
            if (signUpRequest.getLoginType() == ConstantsUtil.LOGIN_GOOGLE) {
                //attributes = socialSignInService.validateGoogleAccessToken(signUpRequest.getIdToken());
                attributes.put("userId", signUpRequest.getEmail());
                attributes.put("email", signUpRequest.getEmail());
                attributes.put("name", signUpRequest.getFirstName() + " " + signUpRequest.getLastName());
            } else if (signUpRequest.getLoginType() == ConstantsUtil.LOGIN_FACEBOOK) {
                if (signUpRequest.getAuthToken() == null || signUpRequest.getAuthToken().isEmpty()) {
                    attributes = socialSignInService.validateFacebookAccessToken(signUpRequest.getIdToken());
                } else {
                    attributes = socialSignInService.validateIosAuthenticationToken(signUpRequest.getAuthToken());

                }
            } else {
                attributes = socialSignInService.validateAppleAccessToken(signUpRequest.getIdToken());
            }
            Optional<Registereduser> registereduserOptional = userService.findByEmail(attributes.get("email").toString());
            Registereduser registereduser;
            Optional<Subscription> subscription;

            if (!registereduserOptional.isPresent()) {

                String token = UUID.randomUUID().toString();
                UserDTO user = new UserDTO();
                user.setFirstName(signUpRequest.getFirstName());
                user.setLastName(signUpRequest.getLastName());
                if (signUpRequest.getEmail() != null && signUpRequest.getEmail().isEmpty()) {
                    user.setUserName(signUpRequest.getFirstName() + signUpRequest.getLastName());
                    user.setEmailId(signUpRequest.getEmail());
                } else if (!attributes.get("email").toString().isEmpty()) {

                    user.setUserName(attributes.get("email").toString());
                    user.setEmailId(attributes.get("email").toString());
                } else {
                    user.setUserName(signUpRequest.getFirstName() + signUpRequest.getLastName());
                    user.setEmailId(signUpRequest.getFirstName() + signUpRequest.getLastName() + "@sisulogs.com");
                }

                if (signUpRequest.getLoginType() == ConstantsUtil.LOGIN_GOOGLE) {
                    user.setAuthType(AuthProvider.google);

                } else if (signUpRequest.getLoginType() == ConstantsUtil.LOGIN_FACEBOOK) {
                    user.setAuthType(AuthProvider.facebook);
                } else {
                    user.setAuthType(AuthProvider.apple);
                }

                user.setRoleName("USER");
                //  user.setPassword(passwordEncoder.encode(signUpRequest.getPassword()));
                user.setMobileNo(signUpRequest.getMobileNo());
                user.setRefreshToken(token);
                user.setHasPassword(1);
                Registereduser result = userService.saveUser(user);

                userService.createPasswordResetTokenForUser(result.getUserid(), token, true);
                registereduser = result;

                if ("RECRUITER".equalsIgnoreCase(user.getRoleName())) {
                    stripeService.checkAndActivateTrialForRecruiter(registereduser);
                }

                //login
                subscription = subscriptionRepo.findByRegistereduser(registereduser);

                UserPrincipal userPrincipal = UserPrincipal.create(registereduser, subscription.orElse(null));

                tokenValue = tokenProvider.createToken(userPrincipal);


                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, new AuthResponse(tokenValue, registereduser.getRefreshToken()),
                        message.getMessage("msg.login_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
            } else {
                registereduser = registereduserOptional.get();
                subscription = subscriptionRepo.findByRegistereduser(registereduser);
                UserPrincipal userPrincipal = UserPrincipal.create(registereduser, subscription.orElse(null));


                tokenValue = tokenProvider.createToken(userPrincipal);
                String result = userService.validatePasswordResetToken(registereduser.getRefreshToken());
                if (result == null) {
                    userService.updateRefreshToken(registereduser.getRefreshToken(), registereduser.getUserid());
                } else {
                    String token = UUID.randomUUID().toString();
                    userService.createPasswordResetTokenForUser(registereduser.getUserid(), token, true);
                    userService.updateRefreshToken(token, registereduser.getUserid());
                    registereduser.setRefreshToken(token);

                }

                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, new AuthResponse(tokenValue, registereduser.getRefreshToken()),
                        message.getMessage("msg.login_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
            }


        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PutMapping("/forgotpassword")
    public ResponseEntity<?> forgotPassword(@RequestParam("email") String userEmail, HttpServletRequest request) {

        try {
            logger.info("Processing forgot password request for email: {}", userEmail);
            Optional<Registereduser> registereduser = userService.findByEmail(userEmail);
            if (registereduser.isPresent()) {
                String token = UUID.randomUUID().toString();
                userService.createPasswordResetTokenForUser(registereduser.get().getUserid(), token, false);
                String messageText = "Please click on link below to reset the password";
                String url = baseUrl + "/auth/user/changePassword?token=" + token;

                String fromEmail = "gmail".equalsIgnoreCase(emailProvider) ? email : domainEmail;
                logger.info("Using email provider: {}, sender email: {}", emailProvider, fromEmail);

                emailService.sendEmail(fromEmail, userEmail, "Reset Your Password - Job Portal", messageText + " \r\n" + url);
                logger.info("Password reset email sent to: {}", userEmail);
            } else {
                logger.warn("Forgot password request for non-existent email: {}", userEmail);
                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, "msg.user_not_found"), HttpStatus.BAD_REQUEST);
            }

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, "Reset password link has been sent to the respective email ID."), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @GetMapping("/user/changePassword")
    public String showChangePasswordPage(HttpServletResponse response,
                                         @RequestParam("token") String token) {
        try {
            String result = userService.validatePasswordResetToken(token);
            if (result != null) {

                response.sendRedirect(baseFrontendUrl + "/login");
            } else {
                String pass = UUID.randomUUID().toString().substring(0, 10);
                //String pass = "admin";
                PasswordResetToken passwordResetToken = userService.findByToken(token).get();
                Registereduser registereduser = userService.findById(passwordResetToken.getUserId()).get();
                int status = userService.updatePassword(passwordEncoder.encode(pass), passwordResetToken.getUserId());
//                SecurityContextHolder.getContext().setAuthentication(null);
//                UsernamePasswordAuthenticationToken userToken =      new UsernamePasswordAuthenticationToken(
//                        registereduser.getUsername(),
//                        pass
//                );
//                Authentication authentication = authenticationManager.authenticate(
//                        userToken
//                );
//
//               // Authentication authentication =   SecurityContextHolder.getContext().getAuthentication();
//
                Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(registereduser);
                UserPrincipal userPrincipal = UserPrincipal.create(registereduser, subscription.orElse(null));
                String newToken = tokenProvider.createToken(userPrincipal);


                response.sendRedirect(baseFrontendUrl + "/resetpassword?token=" + newToken + "&pass=" + pass);
            }

        } catch (Exception e) {
            logger.error(e.getMessage());
            return e.getMessage();
        }
        return "sucess";
    }


    @PostMapping("/refresh-token")
    public ResponseEntity<?> getTokenByRefreshToken(@RequestBody Map<String, Object> map) {
        try {
            String result = userService.validatePasswordResetToken(map.get("token").toString());
            if (result != null) {

                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, true, null, "msg.request_failed"), HttpStatus.BAD_REQUEST);

            } else {


                PasswordResetToken passwordResetToken = userService.findByToken(map.get("token").toString()).get();
                Registereduser registereduser = userService.findById(passwordResetToken.getUserId()).get();
                Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(registereduser);

                UserPrincipal userPrincipal = UserPrincipal.create(registereduser, subscription.orElse(null));
                String newToken = tokenProvider.createToken(userPrincipal);
                userService.updateRefreshToken(map.get("token").toString(), passwordResetToken.getUserId());

                return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, new AuthResponse(newToken, registereduser.getRefreshToken()),
                        message.getMessage("msg.login_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

            }

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

}
