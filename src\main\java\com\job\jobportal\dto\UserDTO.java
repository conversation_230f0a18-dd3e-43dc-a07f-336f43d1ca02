package com.job.jobportal.dto;


import com.job.jobportal.security.AuthProvider;
import com.job.jobportal.validation.PasswordMatches;
import com.job.jobportal.validation.ValidEmail;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@PasswordMatches
@Getter
@Setter
public class UserDTO {

    private Long userid;

    private String userName;

    @NotNull
    @NotEmpty
    private String password;

    @NotNull
    @NotEmpty
    private String confirmPassword;

    @NotNull
    @NotEmpty
    private String firstName;

    private String lastName;

    private String mobileNo;

    @ValidEmail
    @NotNull
    @NotEmpty
    private String emailId;


    private String refreshToken;

    private String notificationToken;

    private String roleName;

    private AuthProvider authType;

    private int hasPassword;

}
