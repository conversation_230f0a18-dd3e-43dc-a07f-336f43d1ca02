package com.job.jobportal.response;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
//import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.HttpClientErrorException;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CustomException {
	@ExceptionHandler(BadCredentialsException.class)
	public ResponseEntity<?> handleBadCredentialsException(Throwable ex) {
		return new ResponseEntity<>(new ApiResponse<>(HttpStatus.UNAUTHORIZED, false, ex.getMessage()),HttpStatus.UNAUTHORIZED);
	}
	@ExceptionHandler(HttpClientErrorException.class)
	public ResponseEntity<?> handleAuthException(Throwable ex) {
		return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, ex.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);
		
	}

//	@ExceptionHandler(InternalAuthenticationServiceException.class)
//	public ResponseEntity<?> handleAuthtestException(Throwable ex) {
//		return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, ex.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);
//	}

	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResponseEntity<?> handleAuthMethodException(Throwable ex) {
		return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, ex.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(Exception.class)
	public ResponseEntity<?> handleException(Throwable ex) {
		return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, ex.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);
	}

}
