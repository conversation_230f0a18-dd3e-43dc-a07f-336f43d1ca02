package com.job.jobportal.repository;

import com.job.jobportal.model.SubscriptionPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SubscriptionPlanRepo extends JpaRepository<SubscriptionPlan,Long> {
    Optional<SubscriptionPlan> findByPlanName(String planName);

    Optional<SubscriptionPlan> findByPlanObject(String planObject);
}
