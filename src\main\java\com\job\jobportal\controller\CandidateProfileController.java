package com.job.jobportal.controller;

import com.job.jobportal.dto.CandidateProfileDTO;
import com.job.jobportal.dto.CandidateProfileResponseDTO;
import com.job.jobportal.model.CandidateProfile;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.service.CandidateProfileService;
import com.job.jobportal.util.CommonUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Locale;

@RestController
@RequestMapping("/api/candidate-profile")
public class CandidateProfileController {

    @Autowired
    private RegisteruserRepository registeruserRepository;

    @Autowired
    private CandidateProfileService candidateProfileService;

    @Autowired
    private MessageSource messageSource;

    private static final Logger logger = LoggerFactory.getLogger(CandidateProfileController.class);


    @GetMapping("/profile")
    public ResponseEntity<?> getCandidateProfile(HttpServletRequest request, Locale locale) {
        try {
            CandidateProfileDTO profile = candidateProfileService.getCandidateProfileByUser(request);
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    profile,
                    messageSource.getMessage("msg.candidate_profile_fetched", null, locale)
                ),
                HttpStatus.OK
            );
        } catch (RuntimeException e) {
            logger.error(e.getMessage());

            CandidateProfileDTO emptyDto = new CandidateProfileDTO();
            try {
                Registereduser user = candidateProfileService.getUserId();
                emptyDto.setUserId(user.getUserid());
                String email = user.getEmail() != null ? user.getEmail() : user.getUsername();
                emptyDto.setCurrentUserEmail(email);
            } catch (Exception ex) {
                logger.error("Failed to get user details: " + ex.getMessage());
            }

            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.OK,
                    false,
                    emptyDto,
                    e.getMessage()
                ),
                HttpStatus.OK
            );
        }
    }


    @GetMapping("/candidates")
    public ResponseEntity<?> getAllCandidates(
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) String skills,
            @RequestParam(required = false) Integer yearsOfExperience,
            @RequestParam(required = false) String addressCountry,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "postedDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection,
            Locale locale) {
        try {
            Page<CandidateProfile> profiles = candidateProfileService.getAllCandidates(
                    isActive, location, skills, yearsOfExperience, addressCountry, sortBy, sortDirection, page, size);
            String message = messageSource.getMessage("msg.candidates_fetched", null, locale);
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, profiles, message),
                    HttpStatus.OK
            );
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                    HttpStatus.BAD_REQUEST
            );
        }
    }


    @PostMapping("/profile")
    public ResponseEntity<?> createCandidateProfile(
            @RequestBody CandidateProfileDTO dto,
            Locale locale) {
        try {
            logger.info("Received candidate profile creation request with job category: {}", dto.getJobCategories());
            CandidateProfileResponseDTO response = candidateProfileService.createCandidateProfile(dto);
            String message = messageSource.getMessage("msg.candidate_profile_created", null, locale);

            ApiResponse<CandidateProfileResponseDTO> apiResponse = new ApiResponse<>(
                HttpStatus.OK, true, response, message
            );
            logger.info("Returning candidate profile response with token: {}", response.hasTokenUpdate());

            return new ResponseEntity<>(apiResponse, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            logger.error("Validation error creating candidate profile: {}", e.getMessage());
            return new ResponseEntity<>(
                new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error("Error creating candidate profile: {}", e.getMessage());

            CandidateProfileDTO emptyDto = new CandidateProfileDTO();
            try {
                Registereduser user = candidateProfileService.getUserId();
                emptyDto.setUserId(user.getUserid());
                String email = user.getEmail() != null ? user.getEmail() : user.getUsername();
                emptyDto.setCurrentUserEmail(email);
            } catch (Exception ex) {
                logger.error("Failed to get user details: " + ex.getMessage());
            }

            return new ResponseEntity<>(
                new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, emptyDto, e.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PutMapping("/update")
    public ResponseEntity<?> updateCandidateProfile(@RequestBody CandidateProfileDTO profileDTO) {
        try {
            logger.info("Received update candidate profile request: {}", profileDTO);

            if (profileDTO.getSkillIds() == null) {
                profileDTO.setSkillIds(new ArrayList<>());
            }

            CandidateProfileResponseDTO response = candidateProfileService.updateCandidateProfile(profileDTO);
            logger.info("Successfully updated candidate profile with ID: {}", response.getProfile().getCandidateProfileId());

            ApiResponse<CandidateProfileResponseDTO> apiResponse = new ApiResponse<>(
                HttpStatus.OK,
                true,
                response,
                messageSource.getMessage("msg.candidate_profile_updated", null, LocaleContextHolder.getLocale())
            );
            logger.info("Returning updated candidate profile response with token: {}", response.hasTokenUpdate());

            return new ResponseEntity<>(apiResponse, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            logger.error("Validation error updating candidate profile: {}", e.getMessage());
            return new ResponseEntity<>(
                new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, e.getMessage()),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            logger.error("Error updating candidate profile: {}", e.getMessage());

            CandidateProfileDTO emptyDto = new CandidateProfileDTO();
            try {
                Registereduser user = candidateProfileService.getUserId();
                emptyDto.setUserId(user.getUserid());
                String email = user.getEmail() != null ? user.getEmail() : user.getUsername();
                emptyDto.setCurrentUserEmail(email);
            } catch (Exception ex) {
                logger.error("Failed to get user details: " + ex.getMessage());
            }

            return new ResponseEntity<>(
                new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, emptyDto, e.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PutMapping("/{candidateProfileId}/status")
    public ResponseEntity<?> updateCandidateStatus(
            @PathVariable Long userId,
            @PathVariable Long candidateProfileId,
            @RequestParam boolean isActive,
            Locale locale) {
        try {
            CandidateProfileDTO profile = candidateProfileService.updateCandidateStatus(userId, candidateProfileId, isActive);

            String messageKey = isActive ? "msg.candidate_activated" : "msg.candidate_deactivated";
            String message = messageSource.getMessage(messageKey, null, locale);

            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, profile, message),
                    HttpStatus.OK
            );
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.NOT_FOUND, false, null, e.getMessage()),
                    HttpStatus.NOT_FOUND
            );
        }
    }

    @GetMapping("/{candidateProfileId}")
    public ResponseEntity<?> getCandidateProfileById(
            @PathVariable Long candidateProfileId,
            Locale locale) {
        try {
            CandidateProfileDTO profile = candidateProfileService.getCandidateProfileById(candidateProfileId);
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    profile,
                    messageSource.getMessage("msg.candidate_profile_fetched", null, locale)
                ),
                HttpStatus.OK
            );
        } catch (RuntimeException e) {
            logger.error("Error retrieving candidate profile: {}", e.getMessage());
            return new ResponseEntity<>(
                new ApiResponse<>(
                    HttpStatus.NOT_FOUND,
                    false,
                    null,
                    e.getMessage()
                ),
                HttpStatus.NOT_FOUND
            );
        }
    }
}
