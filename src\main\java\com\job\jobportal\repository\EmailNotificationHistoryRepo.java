package com.job.jobportal.repository;

import com.job.jobportal.model.EmailNotificationHistory;
import com.job.jobportal.model.Registereduser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface EmailNotificationHistoryRepo extends JpaRepository<EmailNotificationHistory, Long> {

    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.notificationType = :notificationType ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findMostRecentByUserAndType(
            @Param("user") Registereduser user,
            @Param("notificationType") int notificationType);

    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.notificationType = :notificationType " +
           "AND enh.sentDate > :since ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findRecentNotificationsByUserAndType(
            @Param("user") Registereduser user,
            @Param("notificationType") int notificationType,
            @Param("since") Timestamp since);

    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.sentDate BETWEEN :startDate AND :endDate ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findByUserAndDateRange(
            @Param("user") Registereduser user,
            @Param("startDate") Timestamp startDate,
            @Param("endDate") Timestamp endDate);

    @Query("SELECT COUNT(enh) FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.notificationType = :notificationType")
    long countByUserAndType(
            @Param("user") Registereduser user,
            @Param("notificationType") int notificationType);

    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.successful = false " +
           "AND enh.sentDate > :since ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findFailedNotificationsSince(@Param("since") Timestamp since);
}
