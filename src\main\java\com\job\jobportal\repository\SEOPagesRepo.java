package com.job.jobportal.repository;


import com.job.jobportal.model.SEOPages;
import com.job.jobportal.model.SEOPagesTopic;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface SEOPagesRepo extends JpaRepository<SEOPages,Long> {

    @Query("Select c  from SEOPages c WHERE   (:seoPagesTitle is null or c.seoPagesTitle like %:seoPagesTitle% ) Order by c.seoPagesCreatedDate desc")
    Page<SEOPagesTopic> findAllSeoPagetopic(@Param("seoPagesTitle") String seoPagesTitle, Pageable pageable);


    Optional<SEOPages> findBySeoPagesTitle(String seoPagesTitle);

    @Query("SELECT s FROM SEOPages s WHERE LOWER(TRIM(s.seoPagesTitle)) = LOWER(TRIM(:seoPagesTitle))")
    Optional<SEOPages> findBySeoPagesTitle_IgnoreCase(@Param("seoPagesTitle") String seoPagesTitle);

    @Query("Select c from SEOPages c WHERE c.seoPagesUrl = :seoPagesUrl")
    Optional<SEOPages> findBySeoPagesUrl(@Param("seoPagesUrl") String seoPagesUrl);

    @Query("SELECT s FROM SEOPages s WHERE " +
           "(:title IS NULL OR LOWER(s.seoPagesTitle) LIKE LOWER(CONCAT('%', :title, '%'))) AND " +
           "((:category IS NULL) OR " +
           "(:category IS NOT NULL AND s.categoryId IN " +
           "  (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = :categoryComponentTypeId AND " +
           "   (LOWER(m.value) = LOWER(:category) OR LOWER(m.value) LIKE LOWER(CONCAT(:category, '|%')))))) AND " +
           "((:subcategory IS NULL) OR " +
           "(:subcategory IS NOT NULL AND s.subcategoryId IN " +
           "  (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = :subcategoryComponentTypeId AND " +
           "   (LOWER(m.value) = LOWER(:subcategory) OR LOWER(m.value) LIKE LOWER(CONCAT(:subcategory, '|%')))))) AND " +
           "((:subSubcategory IS NULL) OR " +
           "(:subSubcategory IS NOT NULL AND s.subSubcategoryId IN " +
           "  (SELECT m.masterDataId FROM MasterData m WHERE m.componentType.id = :subSubcategoryComponentTypeId AND " +
           "   (LOWER(m.value) = LOWER(:subSubcategory) OR LOWER(m.value) LIKE LOWER(CONCAT(:subSubcategory, '|%'))))))")
    List<SEOPages> findAllByCategorySubcategoryAndSubSubcategoryAndTitle(
            @Param("category") String category,
            @Param("subcategory") String subcategory,
            @Param("subSubcategory") String subSubcategory,
            @Param("title") String title,
            @Param("categoryComponentTypeId") Integer categoryComponentTypeId,
            @Param("subcategoryComponentTypeId") Integer subcategoryComponentTypeId,
            @Param("subSubcategoryComponentTypeId") Integer subSubcategoryComponentTypeId);

    @Query("SELECT s FROM SEOPages s WHERE " +
           "(:title IS NULL OR LOWER(s.seoPagesTitle) LIKE LOWER(CONCAT('%', :title, '%')))")
    List<SEOPages> findByTitleContaining(@Param("title") String title);
}
