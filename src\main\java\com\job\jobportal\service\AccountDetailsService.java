package com.job.jobportal.service;

import com.job.jobportal.model.AccountDetails;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.AccountDetailsRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

@Service
public class AccountDetailsService {

    @Autowired
    AccountDetailsRepo accountDetailsRepo;

    @Autowired
    RegisteruserRepository userRepo;

    @Value("${application.version}")
    private String version;

    private static final Logger logger = LoggerFactory.getLogger(AccountDetailsService.class);


    public Map<String, Object> getAccountDetails() {
        UserPrincipal principal = CommonUtils.getUserPrincipal();
        Registereduser registereduser = userRepo.findByUsername(principal.getUsername());
        Map<String, Object> map = new HashMap<>();
        ObjectMapper oMapper = new ObjectMapper();
        Map<String, Object> registerMap = oMapper.convertValue(registereduser.getAccountDetails(), Map.class);
        double gb = CommonUtils.bytesToGigabytesDecimal(registereduser.getAccountDetails().getStorageUse());
        BigDecimal mb = CommonUtils.bytesToMegabytesBigDecimal(registereduser.getAccountDetails().getStorageUse());
        registerMap.put("storageUseInGb", gb);
        String memoryDisplay = "";
        int planType = principal.getSubscriptionPlanType();
        if (principal.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_CREATED_NOT_PAYED) {
            planType = ConstantsUtil.SUBSCRIPTION_FREE_PLAN;
        }

        switch (planType) {

            case ConstantsUtil.SUBSCRIPTION_FREE_PLAN: {
                memoryDisplay = "1 Gb";
                registerMap.put("storageUseInMb", mb.longValue() / ConstantsUtil.SUBSCRIPTION_LIMIT_FREE_PLAN);
                break;
            }
            case ConstantsUtil.SUBSCRIPTION_BASIC_PLAN: {
                memoryDisplay = ConstantsUtil.SUBSCRIPTION_LIMIT_BASIC_PLAN + " Gb";
                registerMap.put("storageUseInMb", gb / ConstantsUtil.SUBSCRIPTION_LIMIT_BASIC_PLAN);

                break;
            }
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN: {
                memoryDisplay = ConstantsUtil.SUBSCRIPTION_LIMIT_STANDARD_PLAN + " Gb";
                registerMap.put("storageUseInMb", gb / ConstantsUtil.SUBSCRIPTION_LIMIT_STANDARD_PLAN);

                break;
            }
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN: {
                memoryDisplay = ConstantsUtil.SUBSCRIPTION_LIMIT_PREMIUM_PLAN + " Gb";
                registerMap.put("storageUseInMb", gb / ConstantsUtil.SUBSCRIPTION_LIMIT_PREMIUM_PLAN);

                break;
            }
            case ConstantsUtil.SUBSCRIPTION_UNLIMITED_PLAN: {
                memoryDisplay = ConstantsUtil.SUBSCRIPTION_LIMIT_UNLIMITED_PLAN + " Gb";
                registerMap.put("storageUseInMb", gb / ConstantsUtil.SUBSCRIPTION_LIMIT_UNLIMITED_PLAN);

                break;
            }
            default: {
                break;
            }
        }
        if (gb > 1) {
            registerMap.put("storageDisplay", gb + " Gb of " + memoryDisplay);
        } else {
            registerMap.put("storageDisplay", mb + " Mb of " + memoryDisplay);
        }

        map.put("registereduser", registerMap);
        map.put("isLimitReached", checkStorageLimit());
        map.put("appversion", version);
        return map;
    }


    public int blockUser(int isActive, String username) {
        try {
            Registereduser registereduser = userRepo.findByUsername(username);

            return accountDetailsRepo.updateAccountActiveState(isActive, registereduser.getAccountDetails().getAccountDetailsId());

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public int premiumUser(int isPremiumAccount) {
        try {
            Registereduser registereduser = userRepo.findByUsername(CommonUtils.getUserPrincipal().getUsername());

            return accountDetailsRepo.updatePremiumState(isPremiumAccount, registereduser.getAccountDetails().getAccountDetailsId());

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public int deleteAccount(int isDeleteScheduled) {
        try {
            Registereduser registereduser = userRepo.findByUsername(CommonUtils.getUserPrincipal().getUsername());

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 1);
            return accountDetailsRepo.updateDeleteUsage(isDeleteScheduled, new Timestamp(calendar.getTimeInMillis()), registereduser.getAccountDetails().getAccountDetailsId());

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public int updateStorage(Long storageUse, int addFlag, String username) {
        try {
            Registereduser registereduser = userRepo.findByUsername(username);
            AccountDetails accountDetails = accountDetailsRepo.findById(registereduser.getAccountDetails().getAccountDetailsId()).get();
            Long size = accountDetails.getStorageUse();
            if (addFlag == ConstantsUtil.ADD_FILE_STORAGE) {
                size = size + storageUse;
            } else {
                size = size - storageUse;
            }

            return accountDetailsRepo.updateStorageUsage(size, registereduser.getAccountDetails().getAccountDetailsId());

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public boolean checkStorageLimit() {
        boolean flag = false;
        UserPrincipal principal = CommonUtils.getUserPrincipal();
        Registereduser registereduser = userRepo.findByEmail(principal.getEmail()).get();
        Long sizeGb = CommonUtils.bytesToGigabytesLong(registereduser.getAccountDetails().getStorageUse());
        BigDecimal sizeMb = CommonUtils.bytesToMegabytesBigDecimal(registereduser.getAccountDetails().getStorageUse());
        int planType = principal.getSubscriptionPlanType();
        if (principal.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_CREATED_NOT_PAYED) {
            planType = ConstantsUtil.SUBSCRIPTION_FREE_PLAN;
        }

        switch (planType) {

            case ConstantsUtil.SUBSCRIPTION_FREE_PLAN: {
                BigDecimal FinalSize = new BigDecimal(ConstantsUtil.SUBSCRIPTION_LIMIT_FREE_PLAN);
                if (FinalSize.compareTo(sizeMb) <= 0) {
                    flag = true;
                }
                break;
            }
            case ConstantsUtil.SUBSCRIPTION_BASIC_PLAN: {
                if (sizeGb >= ConstantsUtil.SUBSCRIPTION_LIMIT_BASIC_PLAN) {
                    flag = true;
                }
                break;
            }
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN: {
                if (sizeGb >= ConstantsUtil.SUBSCRIPTION_LIMIT_STANDARD_PLAN) {
                    flag = true;
                }
                break;
            }
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN: {
                if (sizeGb >= ConstantsUtil.SUBSCRIPTION_LIMIT_PREMIUM_PLAN) {
                    flag = true;
                }
                break;
            }
            case ConstantsUtil.SUBSCRIPTION_UNLIMITED_PLAN: {
                if (sizeGb >= ConstantsUtil.SUBSCRIPTION_LIMIT_UNLIMITED_PLAN) {
                    flag = true;
                }
                break;
            }
            default: {
                break;
            }
        }
        return flag;

    }


}
