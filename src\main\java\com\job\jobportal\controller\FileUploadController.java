package com.job.jobportal.controller;

import com.job.jobportal.dto.UploadResponse;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.service.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.model.S3Exception;

import java.io.IOException;
import java.util.Locale;

@RestController
public class FileUploadController {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private MessageSource messageSource;

    @PostMapping("/upload")
    public ResponseEntity<?> handleFileUpload(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {

                String errorMsg = messageSource.getMessage("msg.file_cannot_be_empty", null, LocaleContextHolder.getLocale());

                return new ResponseEntity<>(
                        new ApiResponse<>(HttpStatus.BAD_REQUEST, false, null, errorMsg), HttpStatus.BAD_REQUEST);
            }
            UploadResponse response = fileStorageService.uploadFile(file);

            logger.info("File uploaded successfully: {}", response.key());

            String successMsg = messageSource.getMessage("msg.file_uploaded_success", null, LocaleContextHolder.getLocale());

            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.OK, true, response, successMsg), HttpStatus.OK);

        } catch (IOException e) {
            logger.error("File processing error: {}", e.getMessage());

            String errorMsg = messageSource.getMessage("msg.file_processing_error", null, LocaleContextHolder.getLocale());

            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, errorMsg), HttpStatus.INTERNAL_SERVER_ERROR);

        } catch (S3Exception e) {
            logger.error("S3 storage error: {}", e.getMessage());
            String errorMsg = messageSource.getMessage("msg.file_storage_error", null, LocaleContextHolder.getLocale());

            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_GATEWAY, false, null, errorMsg), HttpStatus.BAD_GATEWAY);
        }
    }

    @DeleteMapping("/delete")
    public ResponseEntity<?> deleteFile(
            @RequestParam String key,
            Locale locale) {
        try {
            fileStorageService.deleteFile(key);
            String message = messageSource.getMessage("msg.file_deleted", null, locale);
            return ResponseEntity.ok(
                    new ApiResponse<>(HttpStatus.OK, true, null, message)
            );
        } catch (S3Exception e) {
            logger.error("S3 deletion error: {}", e.getMessage());
            return new ResponseEntity<>(
                    new ApiResponse<>(HttpStatus.BAD_GATEWAY, false, null,
                            messageSource.getMessage("error.file_deletion_failed", null, locale)),
                    HttpStatus.BAD_GATEWAY
            );
        }
    }
}
