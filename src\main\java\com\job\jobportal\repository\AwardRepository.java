package com.job.jobportal.repository;

import com.job.jobportal.model.Award;
import jakarta.data.repository.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AwardRepository extends JpaRepository<Award, Long> {
    @Query("SELECT a FROM Award a WHERE a.resume.resumeId = :resumeId AND a.awardId = :awardId")
    Optional<Award> findByResumeIdAndAwardId(
            @Param("resumeId") Long resumeId,
            @Param("awardId") Long awardId
    );
}