package com.job.jobportal.service;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NotificationService {

    @Autowired
    RegisteruserRepository userRepo;

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    public void sendSingleNotification(String title, String body) {
        UserPrincipal userPrincipal = CommonUtils.getUserPrincipal();
        Registereduser user = userRepo.findById(userPrincipal.getId()).get();
        sendNotification(user.getNotificationToken(), title, body);
    }

    public void sendNotification(String token, String title, String body) {
        Message message = Message.builder()
                .setToken(token)
                .setNotification(Notification.builder()
                        .setTitle(title)
                        .setBody(body)
                        .build())
                .build();

        try {
            String response = FirebaseMessaging.getInstance().send(message);
            System.out.println("Successfully sent message: " + response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void sendBroadcastNotification(String title, String body) {
        // Fetch all tokens from your database
        List<Registereduser> tokens = fetchAllDeviceTokensFromDB();

        for (Registereduser user : tokens) {
            sendNotification(user.getNotificationToken(), title, body);
        }
    }

    private List<Registereduser> fetchAllDeviceTokensFromDB() {
        // Replace this with actual database call
        return userRepo.findAllByNotificationToken();
    }

}
