package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrialSubscriptionResponseDTO {
    private String id;
    private String status;
    private Date currentPeriodStart;
    private Date currentPeriodEnd;
    private boolean cancelAtPeriodEnd;
    private boolean isTrial;
    private String planType;
    private String planName;
    private long daysRemaining;
    private long timeRemaining;
    //private String token;
    private String permissions;

    private int jobPostsLimit;
    private int jobPostsRemaining;
    private Date jobPostsResetDate;
}
