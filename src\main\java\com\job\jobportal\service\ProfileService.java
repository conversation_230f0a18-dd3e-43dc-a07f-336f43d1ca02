package com.job.jobportal.service;

import com.job.jobportal.dto.ProfileDTO;
import com.job.jobportal.model.Profile;
import com.job.jobportal.repository.ProfileRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.util.CommonUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProfileService {

    @Autowired
    ProfileRepo profileRepo;

    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private RegisteruserRepository registeruserRepository;

    private static final Logger logger = LoggerFactory.getLogger(ProfileService.class);


    public ProfileDTO getProfileDetails(Long profileId) {
        Profile profile = profileRepo.findById(profileId).get();
        ProfileDTO profileDTO = modelMapper.map(profile, ProfileDTO.class);
       // AddFamily addFamily = addFamilyRepo.findByProfileidAndUserid(profileId, CommonUtils.getUserPrincipal().getId()).get();
      //  profileDTO.setRelationShip(addFamily.getRelationShip());
        return profileDTO;
    }


    public Profile addProfile(ProfileDTO profileDTO) {

        Long profileId = profileRepo.createProfile(profileDTO.getProfileName(), profileDTO.getProfileDOB(), CommonUtils.getUserPrincipal().getId(), profileDTO.getGender(), profileDTO.getRelationShip());
        Profile profile = modelMapper.map(profileDTO, Profile.class);
        profile.setProfileId(profileId);
        return profile;
    }


    public Profile updateProfile(ProfileDTO profileDTO) {
        Profile profile = modelMapper.map(profileDTO, Profile.class);
//        AddFamily addFamily = addFamilyRepo.findByProfileidAndUserid(profileDTO.getProfileId(), CommonUtils.getUserPrincipal().getId()).get();
//        addFamily.setRelationShip(profileDTO.getRelationShip());
//        addFamilyRepo.save(addFamily);
        return profileRepo.save(profile);
    }

}
