package com.job.jobportal.controller;

import com.job.jobportal.dto.EmailTemplateDTO;
import com.job.jobportal.dto.ReceiveEmailDTO;
import com.job.jobportal.dto.SendMarketingDTO;
import com.job.jobportal.dto.UserBulkEmailRequestDTO;
import com.job.jobportal.model.EmailTemplate;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.job.jobportal.service.EmailTemplateService;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class EmailTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(EmailTemplateController.class);

    @Autowired
    EmailTemplateService emailTemplateService;

    @Autowired
    MessageSource message;

    @PostMapping("/sendemail")
    public ResponseEntity<?> sendEmail(@RequestBody SendMarketingDTO sendMarketingDTO) {
        try {
            emailTemplateService.sendEmail(sendMarketingDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.email_sent_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/receiveemail")
    public ResponseEntity<?> receiveEmail(@RequestBody ReceiveEmailDTO receiveEmailDTO) {
        try {
            emailTemplateService.receiveEmail(receiveEmailDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.email_sent_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/emailtemplate")
    public ResponseEntity<?> getAllEmailtemplate() {
        List<EmailTemplate> marketingUserDTOS = emailTemplateService.getAllEmailTemplate();
        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserDTOS, message.getMessage("msg.emailtemplate_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
    }

    @PostMapping("/emailtemplate")
    public ResponseEntity<?> addEmailTemplate(@RequestBody EmailTemplateDTO emailTemplateDTO) {
        try {
            EmailTemplate emailTemplate = emailTemplateService.addEmailTemplate(emailTemplateDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, emailTemplate, message.getMessage("msg.emailtemplate_added_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/emailtemplate")
    public ResponseEntity<?> updateEmailTemplate(@RequestBody EmailTemplateDTO emailTemplateDTO) {
        try {
            EmailTemplate emailTemplate = emailTemplateService.updateEmailTemplate(emailTemplateDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, emailTemplate, message.getMessage("msg.emailtemplate_updated_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/emailtemplate/{templateId}")
    public ResponseEntity<?> deleteEmailTemplate(@PathVariable("templateId") Long templateId) {
        try {
            emailTemplateService.deleteEmailTemplate(templateId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, message.getMessage("msg.emailtemplate_deleted_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);

        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/send-bulk-email-to-users")
    public ResponseEntity<?> sendBulkEmailToUsers(@Valid @RequestBody UserBulkEmailRequestDTO bulkEmailRequestDTO) {
        try {
            emailTemplateService.sendBulkEmailToUsers(bulkEmailRequestDTO);
            logger.info("Bulk email sent to {} users", bulkEmailRequestDTO.getUserIds().size());
            String successMessage = message.getMessage("msg.bulk_email_sent_success", null, LocaleContextHolder.getLocale());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null, successMessage), HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request for bulk email: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error("Error sending bulk email to users: {}", e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
