package com.job.jobportal.repository;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface SubscriptionRepo extends JpaRepository<Subscription, Long> {

    Optional<Subscription> findByRegistereduser(Registereduser registereduser);

    @Query("SELECT s FROM Subscription s WHERE s.registereduser.userid = :userId")
    Optional<Subscription> findByRegistereduser_Userid(@Param("userId") Long userId);

    @Modifying
    @Query("Update Subscription set subscriptionStatus=:subscriptionStatus  where subscriptionAccountId=:subscriptionAccountId")
    int updateSubscriptionStatus(@Param("subscriptionStatus") int subscriptionStatus,
                                 @Param("subscriptionAccountId") String subscriptionAccountId
    );

    /**
     * Find trial subscriptions that are expiring within the specified number of days
     */
    @Query("SELECT s FROM Subscription s WHERE s.subscriptionStatus = :trialStatus " +
           "AND s.trialEndDate BETWEEN :startDate AND :endDate")
    List<Subscription> findTrialSubscriptionsExpiringBetween(
            @Param("trialStatus") int trialStatus,
            @Param("startDate") Timestamp startDate,
            @Param("endDate") Timestamp endDate);

    /**
     * Find subscriptions that are in buffer period and expiring soon
     * Buffer period subscriptions are those that have ended but are still in grace period
     */
    @Query("SELECT s FROM Subscription s WHERE s.subscriptionStatus = :cancelledStatus " +
           "AND s.lastPaymentDate IS NOT NULL " +
           "AND s.lastPaymentDate BETWEEN :bufferStartDate AND :bufferEndDate")
    List<Subscription> findSubscriptionsInBufferPeriodExpiringBetween(
            @Param("cancelledStatus") int cancelledStatus,
            @Param("bufferStartDate") Timestamp bufferStartDate,
            @Param("bufferEndDate") Timestamp bufferEndDate);

    /**
     * Find subscriptions that have reached their job posting limit
     */
    @Query("SELECT s FROM Subscription s WHERE s.jobPostsRemaining <= 0 " +
           "AND s.jobPostsLimit > 0 " +
           "AND s.subscriptionStatus IN (:activeStatuses)")
    List<Subscription> findSubscriptionsWithJobLimitReached(@Param("activeStatuses") List<Integer> activeStatuses);
}
