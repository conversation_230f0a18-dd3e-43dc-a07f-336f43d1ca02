package com.job.jobportal.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.job.jobportal.dto.EmailTemplateDTO;
import com.job.jobportal.dto.MarketingUserDTO;
import com.job.jobportal.dto.ReceiveEmailDTO;
import com.job.jobportal.dto.SendMarketingDTO;
import com.job.jobportal.dto.UserBulkEmailRequestDTO;
import com.job.jobportal.model.EmailTemplate;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.EmailTemplateRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.util.EmailFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class EmailTemplateService {

    @Value("${application.email}")
    private String emailTo;

    @Autowired
    EmailTemplateRepo emailTemplateRepo;

    @Autowired
    EmailService emailService;

    @Autowired
    RegisteruserRepository registeruserRepository;

    @Autowired
    EmailFormatter emailFormatter;

    @Autowired
    MessageSource messageSource;

    @Value("${email.domain.from}")
    private String fromEmail;

    private static final Logger logger = LoggerFactory.getLogger(EmailTemplateService.class);

    public List<EmailTemplate> getAllEmailTemplate() {
        try {
            return emailTemplateRepo.findAll();

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void sendEmail(
            SendMarketingDTO sendMarketingDTO) {
        try {
            EmailTemplate emailTemplate = emailTemplateRepo.findById(sendMarketingDTO.getTemplateId()).get();

            for (MarketingUserDTO email : sendMarketingDTO.getMarketingUserDTOList()
            ) {
                emailService.sendEmail(emailTo, email.getEmailId(), emailTemplate.getSubject(), emailTemplate.getEmailBody());

            }

        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    public void receiveEmail(
            ReceiveEmailDTO receiveEmailDTO) {
        try {
            String body = "Email recevied from " + receiveEmailDTO.getName() + "\n" + "\n" +
                    "Mobile Number -" + receiveEmailDTO.getMobile() + "\n" + "\n" +
                    "Receiver contact info -" + receiveEmailDTO.getFrom() + "\n" + "\n" +
                    "Receivers message to E-brainee Admin :\n" + "\n" +
                    receiveEmailDTO.getEmailBody();
            emailService.sendEmail(emailTo, emailTo, "Contact Us From E-brainee website:" + receiveEmailDTO.getName(), body);
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        }
    }

    public EmailTemplate addEmailTemplate(EmailTemplateDTO emailTemplateDTO) {
        try {
            EmailTemplate emailTemplate = new EmailTemplate();
            emailTemplate.setEmailBody(emailTemplateDTO.getEmailBody());
            emailTemplate.setSubject(emailTemplateDTO.getSubject());
            return emailTemplateRepo.save(emailTemplate);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public EmailTemplate updateEmailTemplate(EmailTemplateDTO emailTemplateDTO) {
        try {
            EmailTemplate emailTemplate = new EmailTemplate();
            emailTemplate.setEmailBody(emailTemplateDTO.getEmailBody());
            emailTemplate.setSubject(emailTemplateDTO.getSubject());
            emailTemplate.setEmailTemplateId(emailTemplateDTO.getEmailTemplateId());
            return emailTemplateRepo.save(emailTemplate);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    public void deleteEmailTemplate(Long templateId) {
        try {
            emailTemplateRepo.deleteById(templateId);

        } catch (Exception e) {

            logger.error(e.getMessage());
            throw e;
        }
    }

    @Transactional
    public void sendBulkEmailToUsers(UserBulkEmailRequestDTO bulkEmailRequestDTO) {
        try {
            List<Long> userIds = bulkEmailRequestDTO.getUserIds();
            String subject = bulkEmailRequestDTO.getSubject();
            JsonNode bodyNode = bulkEmailRequestDTO.getBody();
            JsonNode signatureNode = bulkEmailRequestDTO.getSignature();

            if (userIds == null || userIds.isEmpty()) {
                throw new IllegalArgumentException(
                        messageSource.getMessage("msg.user_ids_required", null, LocaleContextHolder.getLocale())
                );
            }

            if (subject == null || subject.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        messageSource.getMessage("msg.email_subject_required", null, LocaleContextHolder.getLocale())
                );
            }

            if (bodyNode == null || !bodyNode.isTextual() || bodyNode.asText().trim().isEmpty()) {
                throw new IllegalArgumentException(
                        messageSource.getMessage("msg.email_body_required", null, LocaleContextHolder.getLocale())
                );
            }

            String body = bodyNode.asText();
            String signature = signatureNode != null && signatureNode.isTextual() ? signatureNode.asText() : "";

            logger.info("Preparing to send bulk email to {} users", userIds.size());

            for (Long userId : userIds) {
                Registereduser user = registeruserRepository.findById(userId)
                        .orElse(null);

                if (user == null || user.getEmail() == null || user.getEmail().isEmpty()) {
                    logger.warn("Skipping user ID {} - user not found or email not available", userId);
                    continue;
                }

                String userEmail = user.getEmail();
                String userName = user.getFirstname() + " " + (user.getLastname() != null ? user.getLastname() : "");

                String personalizedBody = "Hi " + userName + ",\n\n" + body;

                String fullBody = personalizedBody;
                if (signature != null && !signature.trim().isEmpty()) {
                    fullBody = personalizedBody + "\n\n" + signature;
                }

                String htmlContent = emailFormatter.formatEmailContent(fullBody);

                try {
                    emailService.sendHtmlEmail(fromEmail, userEmail, subject, htmlContent);
                    logger.info("Sent bulk email to user: {}", userEmail);
                } catch (Exception e) {
                    logger.error("Error sending bulk email to user {}: {}", userEmail, e.getMessage());
                }
            }

            logger.info("Completed sending bulk emails to {} users", userIds.size());
        } catch (Exception e) {
            logger.error("Error in sendBulkEmailToUsers: {}", e.getMessage());
            throw e;
        }
    }

}
