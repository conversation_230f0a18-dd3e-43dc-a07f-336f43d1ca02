package com.job.jobportal.service;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.util.ConstantsUtil;
import com.job.jobportal.util.PermissionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PermissionService {

    @Autowired
    RegisteruserRepository userRepo;

    @Autowired
    private SubscriptionRepo subscriptionRepo;

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    /**
     * Get all permissions for a user
     *
     * @param userId The user ID
     * @return Map of permission codes to boolean values
     */
    public Map<String, Boolean> getUserPermissions(Long userId) {
        Optional<Registereduser> userOpt = userRepo.findById(userId);
        if (!userOpt.isPresent()) {
            logger.warn("User not found with ID: {}", userId);
            return new HashMap<>();
        }

        Registereduser user = userOpt.get();
        Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
        if (!subscriptionOpt.isPresent()) {
            logger.warn("No subscription found for user: {}", userId);
            return new HashMap<>();
        }

        Subscription subscription = subscriptionOpt.get();
        String permissions = subscription.getPermissions();

        if (permissions == null || permissions.isEmpty()) {
            logger.warn("No permissions found for user: {}", userId);
            return new HashMap<>();
        }

        return PermissionUtil.getPermissionMap(permissions);
    }

    /**
     * Check if a user has a specific permission
     *
     * @param userId The user ID
     * @param permissionCode The permission code to check
     * @return true if the user has the permission, false otherwise
     */
    public boolean hasPermission(Long userId, String permissionCode) {
        Optional<Registereduser> userOpt = userRepo.findById(userId);
        if (!userOpt.isPresent()) {
            logger.warn("User not found with ID: {}", userId);
            return false;
        }

        Registereduser user = userOpt.get();
        Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
        if (!subscriptionOpt.isPresent()) {
            logger.warn("No subscription found for user: {}", userId);
            return false;
        }

        Subscription subscription = subscriptionOpt.get();
        String permissions = subscription.getPermissions();

        if (permissions == null || permissions.isEmpty()) {
            logger.warn("No permissions found for user: {}", userId);
            return false;
        }

        int position = getPermissionPosition(permissionCode);

        if (position < 0 || position >= permissions.length()) {
            logger.warn("Invalid permission position: {} for permission: {}", position, permissionCode);
            return false;
        }

        return PermissionUtil.hasPermission(permissions, position);
    }

    /**
     * Get the position of a permission in the permission string
     *
     * @param permissionCode The permission code
     * @return The position (0-based index) of the permission in the permission string
     */
    private int getPermissionPosition(String permissionCode) {
        switch (permissionCode) {
            case "VIEW_APPLICANTS":
                return ConstantsUtil.PERMISSION_VIEW_APPLICANTS;
            case "CONTACT_APPLICANTS":
                return ConstantsUtil.PERMISSION_CONTACT_APPLICANTS;
            case "POST_JOBS":
                return ConstantsUtil.PERMISSION_POST_JOBS;
            case "FEATURED_JOBS":
                return ConstantsUtil.PERMISSION_FEATURED_JOBS;
            case "ANALYTICS":
                return ConstantsUtil.PERMISSION_ANALYTICS;
            case "BULK_ACTIONS":
                return ConstantsUtil.PERMISSION_BULK_ACTIONS;
            default:
                return -1;
        }
    }
}
