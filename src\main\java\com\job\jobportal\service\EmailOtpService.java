package com.job.jobportal.service;

import com.job.jobportal.model.OTP;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.OtpRepo;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.EmailFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

@Service
public class EmailOtpService {

    @Autowired
    OtpRepo otpRepo;

    @Autowired
    EmailService emailService;

    @Autowired
    RegisteruserRepository registeruserRepository;

    @Autowired
    EmailFormatter emailFormatter;

    @Value("${application.email}")
    private String defaultEmail;

    @Value("${email.domain.from}")
    private String domainEmail;

    @Value("${email.provider}")
    private String emailProvider;

    @Value("${application.name}")
    private String name;

    @Value("${app.base-url:https://www.groglojobs.co.uk}")
    private String baseUrl;

    private String getEmail() {
        return "gmail".equalsIgnoreCase(emailProvider) ? defaultEmail : domainEmail;
    }

    public String generateRandomOTP(String emailId) throws Exception {
        List<OTP> existingOtps = otpRepo.findAllByEmailId(emailId);
        if (!existingOtps.isEmpty()) {
            otpRepo.deleteAll(existingOtps);
        }

        SecureRandom random = new SecureRandom();
        int OTP_LENGTH = 6;
        StringBuilder otp = new StringBuilder(OTP_LENGTH);

        String FIRST_DIGIT_CHARS = "123456789";
        otp.append(FIRST_DIGIT_CHARS.charAt(random.nextInt(FIRST_DIGIT_CHARS.length())));

        String OTP_CHARS = "**********";
        for (int i = 1; i < OTP_LENGTH; i++) {
            otp.append(OTP_CHARS.charAt(random.nextInt(OTP_CHARS.length())));
        }

        Calendar expirationTime = Calendar.getInstance();
        // 3 minutes in milliseconds
        long OTP_EXPIRATION_DURATION_MS = 3 * 60 * 1000;
        expirationTime.add(Calendar.MILLISECOND, (int) OTP_EXPIRATION_DURATION_MS);

        OTP otpModal = new OTP();
        otpModal.setEmailId(emailId);
        otpModal.setOtp(Long.parseLong(otp.toString()));
        otpModal.setExpirationTime(new Timestamp(expirationTime.getTimeInMillis()));
        otpRepo.save(otpModal);
        sendEmail(emailId, otpModal.getOtp());
        return otp.toString();
    }

    public boolean verifyOTP(String emailId, Long otp) throws Exception {
        List<OTP> otpList = otpRepo.findMostRecentByEmailId(emailId);
        if (otpList.isEmpty()) {
            throw new Exception("No OTP found for this email");
        }

        OTP otpDetails = otpList.get(0);

        if (otpDetails.getOtp().equals(otp) &&
                System.currentTimeMillis() < otpDetails.getExpirationTime().getTime()) {
            otpRepo.deleteAll(otpList);
            return true;
        }
        return false;
    }

    public String reSendOTP(String emailId) throws Exception {
        List<OTP> existingOtps = otpRepo.findAllByEmailId(emailId);
        if (existingOtps.isEmpty()) {
            throw new Exception("No OTP found for this email");
        }

        otpRepo.deleteAll(existingOtps);
        return generateRandomOTP(emailId);
    }

    public void sendEmail(String emailId, Long otp) {
        String username = registeruserRepository.findByEmail(emailId)
                .map(Registereduser::getUsername)
                .orElse("User");

        String body = "Hi " + username + ",\n\n" +
                "Welcome to GroGloJobs, OTP for your email verification is provided below:\n\n" +
                otp + "\n\n" +
                "If you need assistance <NAME_EMAIL>";

        String htmlContent = emailFormatter.formatEmailContent(body);
        emailService.sendHtmlEmail(getEmail(), emailId, "Job Portal - OTP for Login", htmlContent);
    }


}