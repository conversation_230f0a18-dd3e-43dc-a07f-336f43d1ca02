package com.job.jobportal.repository;

import com.job.jobportal.model.UploadModal;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UploadModalRepo extends JpaRepository<UploadModal, Long> {

    List<UploadModal> findAllByProfileIdOrderByCreatedOnAsc(Long profileId);

    @Query("from UploadModal where userid = :userid and (:isPublic is null or isPublic = :isPublic) and (:year is null or YEAR(createdOn) = :year) AND (:month is null or MONTH(createdOn) = :month) Order by createdOn DESC")
    Page<UploadModal> findAllByUseridOrderByCreatedOnAsc(@Param("userid") Long userId, @Param("isPublic") Integer isPublic, @Param("year") Integer year, @Param("month") Integer month, Pageable pageable);

}
