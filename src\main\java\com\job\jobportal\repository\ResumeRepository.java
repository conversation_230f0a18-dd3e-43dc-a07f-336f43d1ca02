package com.job.jobportal.repository;

import com.job.jobportal.model.Resume;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ResumeRepository extends JpaRepository<Resume, Long> {
    List<Resume> findByCandidateProfile_CandidateProfileId(Long candidateProfileId);
    boolean existsByCandidateProfile_CandidateProfileIdAndTitle(Long candidateProfileId, String title);
}