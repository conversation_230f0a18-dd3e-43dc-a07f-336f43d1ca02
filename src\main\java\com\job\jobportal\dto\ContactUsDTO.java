package com.job.jobportal.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ContactUsDTO {

    @NotBlank(message = "msg.contact_us_name_mandatory")
    private String name;

    @NotBlank(message = "msg.contact_us_email_mandatory")
    @Email(message = "msg.contact_us_email_valid")
    private String email;

    @NotBlank(message = "msg.contact_us_message_mandatory")
    private String message;

    private String phoneNumber;

    private String subject = "Website Contact Us";
}
