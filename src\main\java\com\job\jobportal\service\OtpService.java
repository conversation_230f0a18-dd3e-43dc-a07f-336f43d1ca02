//package com.job.jobportal.service;
//
//import org.springframework.http.*;
//
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Service;
//import org.springframework.web.client.RestTemplate;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//
//import java.util.HashMap;
//import java.util.Map;
//
//@Service
//public class OtpService {
//
//    @Value("${msg91.auth-key}")
//    private String authKey;
//
//    @Value("${msg91.otp-base-url}")
//    private String otpBaseUrl;
//
//    @Value("${msg91.otp-widget-url}")
//    private String otpWidgetUrl;
//
//    @Value("${msg91.template-id}")
//    private String templateId;
//
//     @Autowired
//     RestTemplate restTemplate;
//
//    private static final Logger logger = LoggerFactory.getLogger(OtpService.class);
//
//
//
//    public String sendOtp(String mobileNumber) {
//        String url = otpBaseUrl + "?authkey=" + authKey + "&template_id=" + templateId + "&mobile=" + mobileNumber;
//
//        try {
//            String response = restTemplate.getForObject(url, String.class);
//            return response;
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            throw e;
//        }
//    }
//
//    public String verifyOtp(String mobileNumber, String otp) {
//        String url = otpBaseUrl + "/verify?authkey=" + authKey + "&mobile=" + mobileNumber + "&otp=" + otp;
//
//        try {
//            String response = restTemplate.getForObject(url, String.class);
//            return response;
//        } catch (Exception e) {
//           logger.error(e.getMessage());
//            throw e;
//        }
//    }
//
//    public String resendOtp(String mobileNumber) {
//        String url = otpBaseUrl + "/retry?authkey=" + authKey + "&mobile=" + mobileNumber + "&retrytype=text";
//
//        try {
//            String response = restTemplate.getForObject(url, String.class);
//            return response;
//        } catch (Exception e) {
//         logger.error(e.getMessage());
//            throw e;
//        }
//    }
//
//    public ResponseEntity<String> verifyWidgetOtp(String token) {
//        String url = otpWidgetUrl;
//        Map<String,String> map =new HashMap<>();
//        map.put("authkey",authKey);
//        map.put("access-token",token);
//        try {
//            // Prepare Headers
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//
//            // Prepare Body
//            HttpEntity<Map<String,String>> entity = new HttpEntity<>(map, headers);
//
//            // Call the API
//            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
//
//            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
//
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            throw e;
//        }
//    }
//}
