package com.job.jobportal.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.job.jobportal.dto.*;
import com.job.jobportal.model.*;

import java.sql.Timestamp;
import java.util.*;

import com.job.jobportal.repository.*;
import com.job.jobportal.security.TokenProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.EmailFormatter;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class JobApplicationService {

    @Autowired
    private JobApplicationRepository jobApplicationRepository;

    @Autowired
    private CandidateProfileRepo candidateProfileRepo;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private RegisteruserRepository registeruserRepository;

    @Autowired
    private CompanyProfileRepo companyProfileRepository;

    @Autowired
    private CandidateProfileService candidateProfileService;

    @Autowired
    private TokenProvider tokenProvider;

    @Value("${app.base-url:https://www.groglojobs.co.uk}")
    private String baseUrl;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private MasterDataRepository masterDataRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailFormatter emailFormatter;

    @Value("${application.email}")
    private String fromEmail;

    @Value("${application.aws.import_excel}")
    private String cloudFrontUrl;

    @Autowired
    private JobService jobService;

    private static final Logger logger = LoggerFactory.getLogger(JobApplicationService.class);
    /*
        public Registereduser getUser() {
            return registeruserRepository.findById(CommonUtils.getUserPrincipal().getId())
                    .orElseThrow(() -> new RuntimeException(
                            messageSource.getMessage("msg.registered_user_not_found", null, Locale.getDefault())
                    ));
        }
    */
    private Registereduser getUser() {
        try {
            return registeruserRepository.findById(CommonUtils.getUserPrincipal().getId())
                    .orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    @Transactional
    public JobApplicationFullResponseDTO applyForJob(Long jobPostId, JobApplicationRequest request) {
        Registereduser user = getUser();
        CandidateProfile candidateProfile;

        if (user == null) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.login_required", null, Locale.getDefault())
            );
        }

        if (!hasRole(user, "CANDIDATE")) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }

        if (request != null && request.getAnswer() != null) {
            request.setAnswer(CommonUtils.normalizeText(request.getAnswer()));
        }

        Optional<CandidateProfile> existingProfile = candidateProfileRepo.findByRegistereduser_Userid(user.getUserid());

        if (existingProfile.isPresent()) {
            candidateProfile = existingProfile.get();
            if (request != null) {
                updateCandidateProfileIfNeeded(candidateProfile, request);
            }
            logger.info("Using existing candidate profile ID: {} for user ID: {}", candidateProfile.getCandidateProfileId(), user.getUserid());
        } else {
            if (request == null) {
                throw new IllegalArgumentException(
                        messageSource.getMessage("msg.application_details_required", null, Locale.getDefault())
                );
            }

            candidateProfile = candidateProfileService.createCandidateProfileFromRequest(request);
            candidateProfile.setRegistereduser(user);
            candidateProfile = candidateProfileRepo.save(candidateProfile);

            logger.info("Created minimal candidate profile for job application, but not setting hasCandidateProfile flag");

            logger.info("Created new candidate profile ID: {} for user ID: {}", candidateProfile.getCandidateProfileId(), user.getUserid());
        }

        if (jobApplicationRepository.existsByCandidateProfileRegistereduserAndJobPostJobId(user, jobPostId)) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.already_applied", null, Locale.getDefault())
            );
        }

        JobPost jobPost = jobRepository.findById(jobPostId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_not_found", null, Locale.getDefault())
                ));

        JobApplication application = new JobApplication();
        application.setCandidateProfile(candidateProfile);
        application.setJobPost(jobPost);
        application.setCompanyProfile(jobPost.getCompanyProfile());
        if (request != null) {
            if (request.getResumeUrl() != null && !request.getResumeUrl().trim().isEmpty()) {
                application.setResumeUrl(request.getResumeUrl());
                if (!request.getResumeUrl().equals(candidateProfile.getResumeUrl())) {
                    candidateProfile.setResumeUrl(request.getResumeUrl());
                    candidateProfileRepo.save(candidateProfile);
                    logger.info("Updated candidate profile resume URL for user ID: {}", user.getUserid());
                }
            } else if (candidateProfile.getResumeUrl() != null && !candidateProfile.getResumeUrl().trim().isEmpty()) {
                application.setResumeUrl(candidateProfile.getResumeUrl());
            }

            if (request.getResumeKey() != null && !request.getResumeKey().trim().isEmpty()) {
                application.setResumeKey(request.getResumeKey());

                if ((application.getResumeUrl() == null || application.getResumeUrl().trim().isEmpty()) &&
                    (candidateProfile.getResumeUrl() == null || candidateProfile.getResumeUrl().trim().isEmpty())) {
                    application.setResumeUrl(request.getResumeKey());
                    candidateProfile.setResumeUrl(request.getResumeKey());
                    candidateProfileRepo.save(candidateProfile);
                    logger.info("Set resume URL from resume key for user ID: {}", user.getUserid());
                }
            }
        }

        logger.info("Resume URL for job application: {}", application.getResumeUrl());
        logger.info("Resume Key for job application: {}", application.getResumeKey());
        application.setStatusIdStr("1"); // 1 = Applied

        if (request != null && request.getAnswer() != null && !request.getAnswer().trim().isEmpty()) {
            application.setAnswer(request.getAnswer());
        }

        jobApplicationRepository.save(application);

        UserPrincipal refreshedPrincipal = CommonUtils.refreshUserPrincipal(user.getUserid());
        String jwtToken = tokenProvider.createToken(refreshedPrincipal);

        CandidateProfileDTO dto = mapToCandidateProfileDTO(candidateProfile);
        dto.setUserId(user.getUserid());

        dto.setUpdatedToken(jwtToken);

        logger.info("Candidate (ID: {}) applied for job post ID: {}", user.getUserid(), jobPostId);

        String jobCategoryName = null;
        if (jobPost.getJobCategory() != null) {
            try {
                int categoryId = Integer.parseInt(jobPost.getJobCategory());
                MasterData jobCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(12, categoryId);
                if (jobCategory != null) {
                    jobCategoryName = jobCategory.getValue();
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job category: " + jobPost.getJobCategory());
            }
        }

        String jobSubCategoryName = null;
        if (jobPost.getJobSubCategory() != null) {
            try {
                int subCategoryId = Integer.parseInt(jobPost.getJobSubCategory());
                MasterData jobSubCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(17, subCategoryId);
                if (jobSubCategory != null) {
                    String value = jobSubCategory.getValue();
                    if (value != null && value.contains("|")) {
                        jobSubCategoryName = value.split("\\|")[0];
                    } else {
                        jobSubCategoryName = value;
                    }
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job subcategory: " + jobPost.getJobSubCategory());
            }
        }

        String jobSubSubCategoryName = null;
        if (jobPost.getJobSubSubCategory() != null) {
            try {
                int subSubCategoryId = Integer.parseInt(jobPost.getJobSubSubCategory());
                MasterData jobSubSubCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(23, subSubCategoryId);
                if (jobSubSubCategory != null) {
                    String value = jobSubSubCategory.getValue();
                    if (value != null && value.contains("|")) {
                        jobSubSubCategoryName = value.split("\\|")[0];
                    } else {
                        jobSubSubCategoryName = value;
                    }
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job sub-subcategory: " + jobPost.getJobSubSubCategory());
            }
        }

        String tokenToUse = (dto.getUpdatedToken() != null) ? dto.getUpdatedToken() : jwtToken;
        JobApplicationResponse response = new JobApplicationResponse(dto, tokenToUse, user.getRefreshToken(), jobCategoryName);
        response.setJobSubCategoryName(jobSubCategoryName);
        response.setJobSubSubCategoryName(jobSubSubCategoryName);
        JobApplicationFullResponseDTO fullResponseDTO = new JobApplicationFullResponseDTO(application, jobPost, response);

        populateMasterDataNames(fullResponseDTO);

        sendApplicationNotificationToEmployer(application, user, jobPost);

        sendApplicationConfirmationToCandidate(application, user, jobPost);

        return fullResponseDTO;
    }

    private void updateCandidateProfileIfNeeded(CandidateProfile profile, JobApplicationRequest request) {
        boolean updatedUser = false;
        boolean updatedProfile = false;
        Registereduser user = profile.getRegistereduser();

        if (request.getFullName() != null && !request.getFullName().trim().isEmpty()) {
            if (!request.getFullName().equals(user.getFirstname())) {
                user.setFirstname(request.getFullName());
                updatedUser = true;
            }

            if (profile.getFullName() == null || !request.getFullName().equals(profile.getFullName())) {
                profile.setFullName(request.getFullName());
                updatedProfile = true;
            }
        }

        if (request.getMobileNumber() != null && !request.getMobileNumber().trim().isEmpty()) {
            if (!request.getMobileNumber().equals(user.getMobileno())) {
                user.setMobileno(request.getMobileNumber());
                updatedUser = true;
            }

            if (profile.getPhoneNumber() == null || !request.getMobileNumber().equals(profile.getPhoneNumber())) {
                profile.setPhoneNumber(request.getMobileNumber());
                updatedProfile = true;
            }
        }

        if (request.getEmail() != null && !request.getEmail().trim().isEmpty() &&
                (user.getEmail() == null || !request.getEmail().equals(user.getEmail()))) {
            user.setEmail(request.getEmail());
            updatedUser = true;
        }

        if (request.getResumeKey() != null && !request.getResumeKey().trim().isEmpty()) {
            if (profile.getResumeUrl() == null || profile.getResumeUrl().trim().isEmpty()) {
                profile.setResumeUrl(request.getResumeKey());
                updatedProfile = true;
                logger.info("Set candidate profile resume URL from resume key as fallback");
            }
        }

        if (request.getResumeUrl() != null && !request.getResumeUrl().trim().isEmpty() &&
                !request.getResumeUrl().equals(profile.getResumeUrl())) {
            profile.setResumeUrl(request.getResumeUrl());
            updatedProfile = true;
        }

        if (updatedUser) {
            registeruserRepository.save(user);
            logger.info("Updated Registereduser for user ID: {}", user.getUserid());
        }
        if (updatedProfile) {
            candidateProfileRepo.save(profile);
            logger.info("Updated candidate profile for user ID: {}", user.getUserid());
        }
    }

    private JobApplicationResponseDTO mapToResponseDTO(JobApplication jobApplication, JobPost jobPost) {
        JobApplicationResponseDTO responseDTO = new JobApplicationResponseDTO(jobApplication, jobPost);

        try {
            long totalPositions = jobService.getActiveJobCountForCompany(jobApplication.getCompanyProfile().getCompanyProfileId());
            responseDTO.setCompanyActiveJobCount(totalPositions);
            jobApplication.getCompanyProfile().setTransientActiveJobCount(totalPositions);

            long activeJobPostCount = jobService.getActiveJobPostCountForCompany(jobApplication.getCompanyProfile().getCompanyProfileId());
            jobApplication.getCompanyProfile().setActiveJobCount(activeJobPostCount);
        } catch (Exception e) {
            logger.error("Error setting active job count in response DTO: {}", e.getMessage());
        }

        return responseDTO;
    }

    public boolean hasCandidateApplied(Long jobPostId) {
        Registereduser user = getUser();
        if (!hasRole(user, "CANDIDATE")) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }
        return jobApplicationRepository.existsByCandidateProfileRegistereduserAndJobPostJobId(user, jobPostId);
    }

    public long getJobApplicationCount(Long jobPostId) {
        return jobApplicationRepository.countByJobPostJobId(jobPostId);
    }

    @Transactional
    public JobApplicationDTO updateApplicationStatus(Long applicationId, Integer statusId) {
        Registereduser user = getUser();

        if (!hasRole(user, "RECRUITER")) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }

        JobApplication application = jobApplicationRepository.findByIdWithAssociations(applicationId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_application_not_found", null, Locale.getDefault())
                ));

        MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, statusId);
        if (statusData == null) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_status", null, Locale.getDefault())
            );
        }

        Integer previousStatusId = application.getStatusId();

        application.setStatusId(statusId);

        JobApplication updatedApplication = jobApplicationRepository.save(application);

        logger.info("Recruiter (ID: {}) updated application ID: {} status from {} to {}",
                user.getUserid(), applicationId,
                previousStatusId != null ? previousStatusId : "null",
                statusData.getValue());

        if (previousStatusId == null || !previousStatusId.equals(statusId)) {
            sendStatusUpdateNotification(updatedApplication, statusId, statusData.getValue());
        }

        return mapToDTO(updatedApplication);
    }

    @Transactional(readOnly = true)
    public Page<? extends JobApplicationDTO> getJobApplications(
            String jobTitle,
            String companyName,
            Integer statusId,
            Integer timeFilterId,
            int page,
            int size,
            String sortBy,
            String sortDirection
    ) {
        Registereduser user = getUser();
        String role = user.getRoles().stream().findFirst().get().getRolename();

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.fromString(sortDirection), sortBy));

        Integer timeFilterDays = null;
        Timestamp timeFilterDate = null;
        String timeFilterName = null;

        if (timeFilterId != null) {
            MasterData timeFilterData = masterDataRepository.findByComponentType_IdAndMasterDataId(7, timeFilterId);
            if (timeFilterData != null) {
                timeFilterName = timeFilterData.getValue();
                timeFilterDays = calculateDaysFromTimeFilter(timeFilterId);
                if (timeFilterDays != null) {
                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.DAY_OF_MONTH, -timeFilterDays);
                    timeFilterDate = new Timestamp(cal.getTimeInMillis());
                    logger.info("Applying time filter: {} ({}), filtering from date: {}",
                            timeFilterName, timeFilterDays, timeFilterDate);
                }
            }
        }

        if ("CANDIDATE".equalsIgnoreCase(role)) {
            Page<JobApplication> applications = jobApplicationRepository
                    .findByCandidateProfileRegistereduserAndFilters(
                            user, jobTitle, statusId, timeFilterDays, timeFilterDate, pageable
                    );
            logger.info("Retrieved {} applications for candidate ID: {}", applications.getTotalElements(), user.getUserid());

            final String finalTimeFilterName = timeFilterName;
            return applications.map(app -> {
                JobApplicationDTO dto = mapToDTO(app);
                if (finalTimeFilterName != null) {
                    dto.setTimeFilterName(finalTimeFilterName);
                }
                dto.setHasCandidateProfile(user.isHasCandidateProfile());

                try {
                    long totalPositions = jobService.getActiveJobCountForCompany(app.getCompanyProfile().getCompanyProfileId());
                    dto.setCompanyActiveJobCount(totalPositions);
                    app.getCompanyProfile().setTransientActiveJobCount(totalPositions);

                    long activeJobPostCount = jobService.getActiveJobPostCountForCompany(app.getCompanyProfile().getCompanyProfileId());
                    app.getCompanyProfile().setActiveJobCount(activeJobPostCount);
                } catch (Exception e) {
                    logger.error("Error setting active job count in DTO: {}", e.getMessage());
                }

                return dto;
            });
        } else if ("RECRUITER".equalsIgnoreCase(role)) {
            CompanyProfile companyProfile = registeruserRepository
                    .findCompanyProfileByUserId(user.getUserid())
                    .orElseThrow(() -> new RuntimeException(
                            messageSource.getMessage("msg.company_profile_not_found", null, Locale.getDefault())
                    ))
                    .getCompanyProfile();

            Page<JobApplicationFullDTO> applications = jobApplicationRepository.findByJobPostCompanyProfileAndFilters(
                    companyProfile, jobTitle, companyName, statusId, timeFilterDays, timeFilterDate, pageable
            );
            logger.info("Retrieved {} applications for recruiter ID: {}", applications.getTotalElements(), user.getUserid());

            applications.forEach(app -> {
                if (app.getStatusId() != null) {
                    MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, app.getStatusId());
                    if (statusData != null) {
                        app.setStatusValue(statusData.getValue());
                    }
                }
            });

            final String finalTimeFilterName = timeFilterName;
            if (finalTimeFilterName != null) {
                applications.forEach(app -> app.setTimeFilterName(finalTimeFilterName));
            }

            applications.forEach(app -> {
                app.setHasCandidateProfile(user.isHasCandidateProfile());

                try {
                    long activeJobCount = jobService.getActiveJobCountForCompany(app.getCompanyProfileId());
                    app.setCompanyActiveJobCount(activeJobCount);
                } catch (Exception e) {
                    logger.error("Error setting active job count in DTO: {}", e.getMessage());
                }
            });

            return applications;
        } else {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.invalid_role", null, Locale.getDefault())
            );
        }
    }

    private JobApplicationDTO mapToDTO(JobApplication jobApplication) {
        JobApplicationDTO dto = new JobApplicationDTO();
        dto.setApplicationId(jobApplication.getApplicationId());
        dto.setCandidateProfileId(jobApplication.getCandidateProfile().getCandidateProfileId());
        dto.setCompanyProfileId(jobApplication.getCompanyProfile().getCompanyProfileId());
        dto.setJobPostId(jobApplication.getJobPost().getJobId());
        dto.setJobTitle(jobApplication.getJobPost().getJobTitle());
        dto.setCompanyName(jobApplication.getJobPost().getCompanyProfile().getCompanyName());
        dto.setCompanyLogoUrl(jobApplication.getJobPost().getCompanyProfile().getCompanyLogoURL());
        dto.setJobLocation(jobApplication.getJobPost().getLocation());

        dto.setCompleteAddress(jobApplication.getJobPost().getCompleteAddress());
        String statusIdStr = jobApplication.getStatusIdStr();
        Integer statusId;

        if (statusIdStr != null && statusIdStr.contains("_FROM_APPLIED")) {
            statusId = Integer.parseInt(statusIdStr.split("_")[0]);
        } else {
            statusId = jobApplication.getStatusId();
        }

        dto.setStatusId(statusId);

        if (statusId != null) {
            MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, statusId);
            if (statusData != null) {
                dto.setStatusValue(statusData.getValue());
            }
        }

        dto.setAppliedDate(jobApplication.getAppliedDate());
        dto.setUpdatedDate(jobApplication.getUpdatedDate());
        dto.setSkills(jobApplication.getCandidateProfile().getSkills());

        try {
            CompanyProfile companyProfile = jobApplication.getCompanyProfile();
            if (companyProfile != null) {
                long totalPositions = jobService.getActiveJobCountForCompany(companyProfile.getCompanyProfileId());
                dto.setCompanyActiveJobCount(totalPositions);
                companyProfile.setTransientActiveJobCount(totalPositions);

                long activeJobPostCount = jobService.getActiveJobPostCountForCompany(companyProfile.getCompanyProfileId());
                companyProfile.setActiveJobCount(activeJobPostCount);
            }
        } catch (Exception e) {
            logger.error("Error setting active job count in DTO: {}", e.getMessage());
        }

        if (jobApplication.getResumeUrl() != null) {
            String resumeUrl = jobApplication.getResumeUrl();
            if (!resumeUrl.startsWith("http")) {
                if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                    resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
                }
                dto.setResumeUrl(cloudFrontUrl + "/" + resumeUrl);
            } else {
                if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                    resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
                }
                dto.setResumeUrl(resumeUrl);
            }
        }

        JobPost jobPost = jobApplication.getJobPost();
        CandidateProfile candidateProfile = jobApplication.getCandidateProfile();

        setMasterDataFields(jobPost.getJobCategory(), 12, dto::setJobCategoryId, dto::setJobCategoryName, "job category");
        setMasterDataFields(jobPost.getJobSubCategory(), 17, dto::setJobSubCategoryId, dto::setJobSubCategoryName, "job subcategory");
        setMasterDataFields(jobPost.getJobSubSubCategory(), 23, dto::setJobSubSubCategoryId, dto::setJobSubSubCategoryName, "job sub-subcategory");
        setMasterDataFields(candidateProfile.getSalaryCurrency(), 14, dto::setSalaryCurrencyId, dto::setSalaryCurrencyName, "salary currency");
        setMasterDataFields(candidateProfile.getExperience(), 5, dto::setExperienceId, dto::setExperienceName, "experience");
        setMasterDataFields(jobPost.getJobType(), 13, dto::setJobTypeId, dto::setJobTypeName, "job type");
        setMasterDataFields(jobPost.getCareerLevel(), 4, dto::setCareerLevelId, dto::setCareerLevelName, "career level");
        setMasterDataFields(jobPost.getIndustry(), 9, dto::setIndustryId, dto::setIndustryName, "industry");
        dto.setIndustryValue(jobPost.getIndustry());
        setMasterDataFields(jobPost.getQualification(), 8, dto::setQualificationId, dto::setQualificationName, "qualification");
        setMasterDataFields(jobPost.getPayType(), 15, dto::setPayTypeId, dto::setPayTypeName, "pay type");
        setMasterDataFields(jobPost.getDepartmentName(), 2, dto::setDepartmentId, dto::setDepartmentName, "department");

        return dto;
    }

    private CandidateProfileDTO mapToCandidateProfileDTO(CandidateProfile candidateProfile) {
        return modelMapper.map(candidateProfile, CandidateProfileDTO.class);
    }

    private void setMasterDataFields(String idValue, int componentTypeId,
                                    java.util.function.Consumer<Integer> idSetter,
                                    java.util.function.Consumer<String> nameSetter,
                                    String fieldName) {
        if (idValue != null && !idValue.isEmpty()) {
            try {
                int id = Integer.parseInt(idValue);
                idSetter.accept(id);
                MasterData masterData = masterDataRepository.findByComponentType_IdAndMasterDataId(componentTypeId, id);
                if (masterData != null) {
                    nameSetter.accept(masterData.getValue());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse " + fieldName + ": " + idValue);
            }
        }
    }

    private String getMasterDataValue(String idValue, int componentTypeId) {
        if (idValue != null && !idValue.isEmpty()) {
            try {
                int id = Integer.parseInt(idValue);
                MasterData masterData = masterDataRepository.findByComponentType_IdAndMasterDataId(componentTypeId, id);
                if (masterData != null) {
                    return masterData.getValue();
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse master data ID: " + idValue + " for component type: " + componentTypeId);
            }
        }
        return "";
    }

    private String getResumeUrlForApplicant(JobApplication application, CandidateProfile candidateProfile) {
        logger.info("Getting resume URL for application ID: {}", application.getApplicationId());

        if (application.getResumeUrl() != null && !application.getResumeUrl().trim().isEmpty()) {
            logger.info("Using application resume URL: {}", application.getResumeUrl());
            String resumeUrl = application.getResumeUrl();
            if (!resumeUrl.startsWith("http")) {
                if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                    resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
                }
                return cloudFrontUrl + "/" + resumeUrl;
            }
            if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
            }
            return resumeUrl;
        }

        if (candidateProfile.getResumeUrl() != null && !candidateProfile.getResumeUrl().trim().isEmpty()) {
            logger.info("Using candidate profile resume URL: {}", candidateProfile.getResumeUrl());
            String resumeUrl = candidateProfile.getResumeUrl();
            if (!resumeUrl.startsWith("http")) {
                if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                    resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
                }
                return cloudFrontUrl + "/" + resumeUrl;
            }
            if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
            }
            return resumeUrl;
        }

        if (application.getResumeKey() != null && !application.getResumeKey().trim().isEmpty()) {
            logger.info("Using application resume key as fallback: {}", application.getResumeKey());
            String resumeKey = application.getResumeKey();
            if (resumeKey.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                resumeKey = resumeKey.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
            }
            return cloudFrontUrl + "/" + resumeKey;
        }

        logger.warn("No resume URL found for application ID: {}", application.getApplicationId());
        return null;
    }

    /**
     * Calculate the number of days to filter based on the time filter ID
     * @param timeFilterId The ID of the time filter from master data (component_type_id=7)
     * @return The number of days to filter, or null if no filtering should be applied
     */
    private Integer calculateDaysFromTimeFilter(Integer timeFilterId) {
        if (timeFilterId == null) return null;

        // Based on the master data values in schema.sql for component_type_id=7
        switch (timeFilterId) {
            case 1: // Today
                return 1;
            case 2: // Last 7 days
                return 7;
            case 3: // Last 30 days
                return 30;
            case 4: // Last 45 days
                return 45;
            case 5: // Last 60 days
                return 60;
            case 6: // Last 90 days
                return 90;
            case 7: // Last 6 months
                return 180;
            case 8: // Last 12 months
                return 365;
            case 9: // Last 24 months
                return 730;
            case 10: // Last 5 years
                return 365 * 5;
            case 11: // Last 7 years
                return 365 * 7;
            default:
                return null;
        }
    }

    private Integer parseMasterDataId(String idValue) {
        if (idValue != null && !idValue.isEmpty()) {
            try {
                return Integer.parseInt(idValue);
            } catch (NumberFormatException e) {
                logger.error("Failed to parse master data ID: " + idValue);
            }
        }
        return null;
    }

    private String getFormattedJobLocation(JobPost jobPost) {
        String jobLocation = "Not specified";
        if (jobPost.getLocation() != null && !jobPost.getLocation().isEmpty()) {
            try {
                int locationId = Integer.parseInt(jobPost.getLocation());
                MasterData locationData = masterDataRepository.findByComponentType_IdAndMasterDataId(3, locationId);
                if (locationData != null) {
                    jobLocation = locationData.getValue();
                    logger.info("Converted job location ID {} to value: {}", locationId, jobLocation);
                } else {
                    jobLocation = jobPost.getLocation();
                    logger.warn("Job location ID {} not found in master data", locationId);
                }
            } catch (NumberFormatException e) {
                jobLocation = jobPost.getLocation();
                logger.debug("Job location is not an ID, using as is: {}", jobLocation);
            }
        } else if (jobPost.getCity() != null && !jobPost.getCity().isEmpty()) {
            jobLocation = jobPost.getCity();
            if (jobPost.getCountry() != null && !jobPost.getCountry().isEmpty()) {
                jobLocation += ", " + jobPost.getCountry();
            }
        } else if (jobPost.getCountry() != null && !jobPost.getCountry().isEmpty()) {
            jobLocation = jobPost.getCountry();
        }
        return jobLocation;
    }

    private String getFormattedCandidateName(CandidateProfile candidateProfile, Registereduser candidate) {
        String candidateName = "Not specified";
        if (candidateProfile != null && candidateProfile.getFullName() != null && !candidateProfile.getFullName().isEmpty()) {
            candidateName = candidateProfile.getFullName();
        } else if (candidate != null) {
            String firstName = candidate.getFirstname() != null ? candidate.getFirstname() : "";
            String lastName = candidate.getLastname() != null ? candidate.getLastname() : "";
            if (!firstName.isEmpty() || !lastName.isEmpty()) {
                candidateName = firstName + " " + lastName;
            }
        }
        return candidateName;
    }

    @Async
    public void sendApplicationNotificationToEmployer(JobApplication application, Registereduser candidate, JobPost jobPost) {
        try {
            CompanyProfile companyProfile = jobPost.getCompanyProfile();
            Registereduser employer = jobPost.getPostedBy();
            String employerEmail = employer != null ? employer.getEmail() : companyProfile.getCompanyEmail();

            if (employerEmail == null || employerEmail.isEmpty()) {
                logger.error("Cannot send application notification: employer email not found");
                return;
            }

            CandidateProfile candidateProfile = application.getCandidateProfile();

            String subject = "New Job Application: " + jobPost.getJobTitle();

            String jobDetailsUrl = baseUrl + "/job-single-v1/" + jobPost.getJobId();

            StringBuilder body = new StringBuilder();
            body.append("Dear Employer,\n\n");
            body.append("A new candidate has applied for your job posting.\n\n");

            body.append("Job Details:\n");
            body.append("Title: ").append(jobPost.getJobTitle()).append("\n");
            body.append("Job ID: ").append(jobPost.getJobId()).append(" (View details: ").append(jobDetailsUrl).append(")\n");

            String jobLocation = getFormattedJobLocation(jobPost);
            body.append("Location: ").append(jobLocation).append("\n\n");

            body.append("Candidate Details:\n");
            String candidateName = getFormattedCandidateName(candidateProfile, candidate);
            body.append("Name: ").append(candidateName).append("\n");

            if (candidateProfile != null) {
                if (candidateProfile.getDesignation() != null && !candidateProfile.getDesignation().isEmpty()) {
                    body.append("Designation: ").append(candidateProfile.getDesignation()).append("\n");
                }

                if (candidateProfile.getExperience() != null && !candidateProfile.getExperience().isEmpty()) {
                    String experienceName = "";
                    try {
                        int experienceId = Integer.parseInt(candidateProfile.getExperience());
                        MasterData experienceData = masterDataRepository.findByComponentType_IdAndMasterDataId(5, experienceId);
                        if (experienceData != null) {
                            experienceName = experienceData.getValue();
                        }
                    } catch (NumberFormatException e) {
                        experienceName = candidateProfile.getExperience();
                    }
                    body.append("Experience: ").append(experienceName).append("\n");
                }

                if (candidateProfile.getSkills() != null && !candidateProfile.getSkills().isEmpty()) {
                    body.append("Skills: ").append(candidateProfile.getSkills()).append("\n");
                }

                String candidateLocation = getCandidateLocation(candidateProfile);
                body.append("Location: ").append(candidateLocation).append("\n");
            }

            if (application.getResumeUrl() != null && !application.getResumeUrl().isEmpty()) {
                String resumeUrl = getResumeUrl(application);
                body.append("\nResume: ").append(resumeUrl).append("\n");
            }

            if (jobPost.getQuestion() != null && !jobPost.getQuestion().isEmpty()) {
                body.append("\nApplication Question: ").append(jobPost.getQuestion()).append("\n");

                if (application.getAnswer() != null && !application.getAnswer().isEmpty()) {
                    body.append("Candidate's Answer: ").append(application.getAnswer()).append("\n");
                } else {
                    body.append("Candidate's Answer: No answer provided\n");
                }
            } else if (application.getAnswer() != null && !application.getAnswer().isEmpty()) {
                body.append("\nCandidate's Answer: ").append(application.getAnswer()).append("\n");
            }

            body.append("\nYou can view all applications for this job in your employer dashboard.\n");
            body.append("Login to your employer dashboard: ").append(baseUrl).append("/employers-dashboard/dashboard");

            String htmlContent = emailFormatter.formatEmailContent(body.toString());
            emailService.sendHtmlEmail(fromEmail, employerEmail, subject, htmlContent);

            logger.info("Sent application notification to employer: {}", employerEmail);
        } catch (Exception e) {
            logger.error("Error sending application notification: " + e.getMessage(), e);
        }
    }

    private String getResumeUrl(JobApplication application) {
        String resumeUrl = application.getResumeUrl();
        if (!resumeUrl.startsWith("http")) {
            if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
            }
            resumeUrl = cloudFrontUrl + "/" + resumeUrl;
        } else {
            if (resumeUrl.contains("ebrainyvideostreaming/ebrainyvideostreaming/")) {
                resumeUrl = resumeUrl.replace("ebrainyvideostreaming/ebrainyvideostreaming/", "ebrainyvideostreaming/");
            }
        }
        return resumeUrl;
    }

    private static String getCandidateLocation(CandidateProfile candidateProfile) {
        String candidateLocation = "Not specified";
        if (candidateProfile.getLocation() != null && !candidateProfile.getLocation().isEmpty()) {
            candidateLocation = candidateProfile.getLocation();
        } else if (candidateProfile.getAddressCity() != null && !candidateProfile.getAddressCity().isEmpty()) {
            candidateLocation = candidateProfile.getAddressCity();
            if (candidateProfile.getAddressCountry() != null && !candidateProfile.getAddressCountry().isEmpty()) {
                candidateLocation += ", " + candidateProfile.getAddressCountry();
            }
        } else if (candidateProfile.getAddressCountry() != null && !candidateProfile.getAddressCountry().isEmpty()) {
            candidateLocation = candidateProfile.getAddressCountry();
        }
        return candidateLocation;
    }

    @Async
    public void sendApplicationConfirmationToCandidate(JobApplication application, Registereduser candidate, JobPost jobPost) {
        try {
            String candidateEmail = candidate.getEmail();

            if (candidateEmail == null || candidateEmail.isEmpty()) {
                logger.error("Cannot send application confirmation: candidate email not found");
                return;
            }

            CandidateProfile candidateProfile = application.getCandidateProfile();
            String subject = "Job Application Confirmation: " + jobPost.getJobTitle();

            StringBuilder body = new StringBuilder();
            String candidateName = getFormattedCandidateName(candidateProfile, candidate);
            body.append("Dear ").append(candidateName).append(",\n\n");
            body.append("Thank you for applying to the following position:\n\n");

            body.append("Job Details:\n");
            body.append("Title: ").append(jobPost.getJobTitle()).append("\n");
            body.append("Company: ").append(jobPost.getCompanyProfile().getCompanyName()).append("\n");

            String jobLocation = getFormattedJobLocation(jobPost);
            body.append("Location: ").append(jobLocation).append("\n");

            body.append("\nYour application has been submitted successfully and is now under review. ");
            body.append("You will receive updates as your application progresses.");

            String htmlContent = emailFormatter.formatEmailContent(body.toString());
            emailService.sendHtmlEmail(fromEmail, candidateEmail, subject, htmlContent);

            logger.info("Sent application confirmation to candidate: {}", candidateEmail);
        } catch (Exception e) {
            logger.error("Error sending application confirmation: " + e.getMessage(), e);
        }
    }

    @Async
    public void sendStatusUpdateNotification(JobApplication application, Integer statusId, String statusName) {
        try {
            if (statusId == 5) {
                logger.info("Skipping notification for rejection status");
                return;
            }

            CandidateProfile candidateProfile = application.getCandidateProfile();
            Registereduser candidate = candidateProfile.getRegistereduser();
            String candidateEmail = candidate.getEmail();

            if (candidateEmail == null || candidateEmail.isEmpty()) {
                logger.error("Cannot send status update notification: candidate email not found");
                return;
            }

            JobPost jobPost = application.getJobPost();

            String subject = "Your Job Application Status Updated";

            String jobDetailsUrl = baseUrl + "/job-single-v1/" + jobPost.getJobId();

            StringBuilder body = new StringBuilder();
            String candidateName = getFormattedCandidateName(candidateProfile, candidate);
            body.append("Dear ").append(candidateName).append(",\n\n");
            body.append("Your application status has been updated.\n\n");

            body.append("Job Details:\n");
            body.append("Title: ").append(jobPost.getJobTitle()).append(" (View job: ").append(jobDetailsUrl).append(")\n");
            body.append("Company: ").append(jobPost.getCompanyProfile().getCompanyName()).append("\n");

            String jobLocation = getFormattedJobLocation(jobPost);
            body.append("Location: ").append(jobLocation).append("\n\n");

            body.append("Application Status: ").append(statusName).append("\n\n");

            switch (statusId) {
                case 2: // Shortlisted
                    body.append("Congratulations! Your application has been shortlisted. The employer is reviewing your profile in detail.\n");
                    break;
                case 3: // Interview
                    body.append("Great news! The employer would like to interview you. They will contact you soon with more details.\n");
                    break;
                case 4: // Hired
                    body.append("Congratulations! You have been selected for this position. The employer will contact you with next steps.\n");
                    break;
                default:
                    body.append("The employer has updated your application status. You can view more details in your candidate dashboard.\n");
            }

            body.append("\nYou can view all your applications in your candidate dashboard.\n");
            body.append("Login to your candidate dashboard: ").append(baseUrl).append("/candidates-dashboard/dashboard");

            String htmlContent = emailFormatter.formatEmailContent(body.toString());
            emailService.sendHtmlEmail(fromEmail, candidateEmail, subject, htmlContent);

            logger.info("Sent status update notification to candidate: {}", candidateEmail);
        } catch (Exception e) {
            logger.error("Error sending status update notification: " + e.getMessage(), e);
        }
    }

    @Transactional
    public void sendBulkEmail(BulkEmailRequestDTO bulkEmailRequestDTO) {
        Registereduser user = getUser();

        if (!hasRole(user, "RECRUITER")) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }

        CompanyProfile companyProfile = registeruserRepository
                .findCompanyProfileByUserId(user.getUserid())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.company_profile_not_found", null, Locale.getDefault())
                ))
                .getCompanyProfile();

        if (companyProfile.getCompanyName() == null || companyProfile.getCompanyName().isEmpty() ||
            companyProfile.getCompanyEmail() == null || companyProfile.getCompanyEmail().isEmpty()) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.company_profile_incomplete", null, Locale.getDefault())
            );
        }

        List<Long> applicantIds = bulkEmailRequestDTO.getApplicantIds();
        String subject = bulkEmailRequestDTO.getSubject();
        JsonNode bodyNode = bulkEmailRequestDTO.getBody();
        JsonNode signatureNode = bulkEmailRequestDTO.getSignature();

        if (applicantIds == null || applicantIds.isEmpty()) {
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.applicant_ids_required", null, Locale.getDefault())
            );
        }

        if (subject == null || subject.trim().isEmpty()) {
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.email_subject_required", null, Locale.getDefault())
            );
        }

        if (bodyNode == null || !bodyNode.isTextual() || bodyNode.asText().trim().isEmpty()) {
            throw new IllegalArgumentException(
                    messageSource.getMessage("msg.email_body_required", null, Locale.getDefault())
            );
        }

        String body = bodyNode.asText();
        String signature = signatureNode != null && signatureNode.isTextual() ? signatureNode.asText() : "";

        List<JobApplication> applications = new ArrayList<>();
        for (Long applicationId : applicantIds) {
            JobApplication application = jobApplicationRepository.findByIdWithAssociations(applicationId)
                    .orElseThrow(() -> new RuntimeException(
                            messageSource.getMessage("msg.job_application_not_found", null, Locale.getDefault()) + ": " + applicationId
                    ));

            if (!application.getCompanyProfile().getCompanyProfileId().equals(companyProfile.getCompanyProfileId())) {
                logger.error("Permission denied: Application {} belongs to company {} but user is from company {}",
                    applicationId,
                    application.getCompanyProfile().getCompanyProfileId(),
                    companyProfile.getCompanyProfileId());
                throw new RuntimeException(
                        messageSource.getMessage("msg.no_permission", null, Locale.getDefault()) + ": " + applicationId
                );
            }

            applications.add(application);
        }

        for (JobApplication application : applications) {
            CandidateProfile candidateProfile = application.getCandidateProfile();
            String candidateEmail = candidateProfile.getRegistereduser().getEmail();

            if (candidateEmail == null || candidateEmail.isEmpty()) {
                logger.warn("Skipping email to applicant with ID {} - email not found", application.getApplicationId());
                continue;
            }

            try {
                String fullBody = body;
                if (signature != null && !signature.trim().isEmpty()) {
                    fullBody = body + signature;
                }
                emailService.sendBulkHtmlEmail(fromEmail, candidateEmail, subject, fullBody, companyProfile);
                logger.info("Sent HTML bulk email to candidate: {}", candidateEmail);
            } catch (Exception e) {
                logger.error("Error sending bulk email to candidate {}: {}", candidateEmail, e.getMessage());
            }
        }

        logger.info("Completed sending bulk emails to {} applicants", applications.size());
    }

    private void populateMasterDataNames(JobApplicationFullResponseDTO dto) {
        if (dto.getLocationId() != null) {
            MasterData locationData = masterDataRepository.findByComponentType_IdAndMasterDataId(3, dto.getLocationId());
            if (locationData != null) {
                dto.setLocationName(locationData.getValue());
            }
        }

        if (dto.getJobTypeId() != null) {
            MasterData jobTypeData = masterDataRepository.findByComponentType_IdAndMasterDataId(13, dto.getJobTypeId());
            if (jobTypeData != null) {
                dto.setJobTypeName(jobTypeData.getValue());
            }
        }

        if (dto.getCareerLevelId() != null) {
            MasterData careerLevelData = masterDataRepository.findByComponentType_IdAndMasterDataId(4, dto.getCareerLevelId());
            if (careerLevelData != null) {
                dto.setCareerLevelName(careerLevelData.getValue());
            }
        }

        if (dto.getExperienceId() != null) {
            MasterData experienceData = masterDataRepository.findByComponentType_IdAndMasterDataId(5, dto.getExperienceId());
            if (experienceData != null) {
                dto.setExperienceName(experienceData.getValue());
            }
        }

        if (dto.getIndustryId() != null) {
            MasterData industryData = masterDataRepository.findByComponentType_IdAndMasterDataId(9, dto.getIndustryId());
            if (industryData != null) {
                dto.setIndustryName(industryData.getValue());
            }
        }

        if (dto.getQualificationId() != null) {
            MasterData qualificationData = masterDataRepository.findByComponentType_IdAndMasterDataId(8, dto.getQualificationId());
            if (qualificationData != null) {
                dto.setQualificationName(qualificationData.getValue());
            }
        }

        if (dto.getJobCategoryId() != null && dto.getJobCategoryName() == null) {
            MasterData jobCategoryData = masterDataRepository.findByComponentType_IdAndMasterDataId(12, dto.getJobCategoryId());
            if (jobCategoryData != null) {
                dto.setJobCategoryName(jobCategoryData.getValue());
            }
        }
        if (dto.getJobSubCategoryId() != null && dto.getJobSubCategoryName() == null) {
            MasterData jobSubCategoryData = masterDataRepository.findByComponentType_IdAndMasterDataId(17, dto.getJobSubCategoryId());
            if (jobSubCategoryData != null) {
                String value = jobSubCategoryData.getValue();
                if (value != null && value.contains("|")) {
                    dto.setJobSubCategoryName(value.split("\\|")[0]);
                } else {
                    dto.setJobSubCategoryName(value);
                }
            }
        }

        if (dto.getJobSubSubCategoryId() != null && dto.getJobSubSubCategoryName() == null) {
            MasterData jobSubSubCategoryData = masterDataRepository.findByComponentType_IdAndMasterDataId(23, dto.getJobSubSubCategoryId());
            if (jobSubSubCategoryData != null) {
                String value = jobSubSubCategoryData.getValue();
                if (value != null && value.contains("|")) {
                    dto.setJobSubSubCategoryName(value.split("\\|")[0]);
                } else {
                    dto.setJobSubSubCategoryName(value);
                }
            }
        }

        if (dto.getSalaryCurrencyId() != null) {
            MasterData salaryCurrencyData = masterDataRepository.findByComponentType_IdAndMasterDataId(14, dto.getSalaryCurrencyId());
            if (salaryCurrencyData != null) {
                dto.setSalaryCurrencyName(salaryCurrencyData.getValue());
            }
        }

        if (dto.getPayTypeId() != null) {
            MasterData payTypeData = masterDataRepository.findByComponentType_IdAndMasterDataId(15, dto.getPayTypeId());
            if (payTypeData != null) {
                dto.setPayTypeName(payTypeData.getValue());
            }
        }
    }

    private boolean hasRole(Registereduser user, String roleName) {
        return user.getRoles().stream().anyMatch(role -> role.getRolename().equalsIgnoreCase(roleName));
    }

    private JobApplicationDTO mapFullDTOToDTO(JobApplicationFullDTO fullDTO) {
        JobApplicationDTO dto = new JobApplicationDTO();
        dto.setApplicationId(fullDTO.getApplicationId());
        dto.setCandidateProfileId(fullDTO.getCandidateProfileId());
        dto.setCompanyProfileId(fullDTO.getCompanyProfileId());
        dto.setJobPostId(fullDTO.getJobPostId());
        dto.setJobTitle(fullDTO.getJobTitle());
        dto.setIndustryName(fullDTO.getIndustry());
        dto.setJobLocation(fullDTO.getJobLocation());
        dto.setCompanyName(fullDTO.getCompanyName());
        dto.setCompanyLogoUrl(fullDTO.getCompanyLogoUrl());
        dto.setStatusId(fullDTO.getStatusId());
        dto.setStatusValue(fullDTO.getStatusValue());
        dto.setAppliedDate(fullDTO.getAppliedDate());
        dto.setUpdatedDate(fullDTO.getUpdatedDate());
        dto.setSkills(fullDTO.getSkills());
        dto.setJobCategoryName(fullDTO.getJobCategory());
        dto.setJobCategoryName(fullDTO.getJobCategoryName());
        dto.setJobSubCategoryName(fullDTO.getJobSubCategory());
        dto.setJobSubCategoryName(fullDTO.getJobSubCategoryName());
        dto.setJobSubSubCategoryName(fullDTO.getJobSubSubCategory());
        dto.setJobSubSubCategoryName(fullDTO.getJobSubSubCategoryName());
        dto.setCompleteAddress(fullDTO.getCompleteAddress());

        JobPost jobPost = jobRepository.findById(fullDTO.getJobPostId())
                .orElse(null);

        if (jobPost != null) {
            fullDTO.setJobCategory(jobPost.getJobCategory());
            fullDTO.setCity(jobPost.getCity());
            fullDTO.setCountry(jobPost.getCountry());
        }

        return dto;
    }

    @Transactional(readOnly = true)
    public JobApplicantsResponseDTO getAllJobsWithApplicants() {
        Registereduser user = getUser();

        if (!hasRole(user, "RECRUITER")) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }

        CompanyProfile companyProfile = registeruserRepository
                .findCompanyProfileByUserId(user.getUserid())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.company_profile_not_found", null, Locale.getDefault())
                ))
                .getCompanyProfile();

        List<JobPost> companyJobs = jobRepository.findByCompanyProfileCompanyProfileId(companyProfile.getCompanyProfileId());

        if (companyJobs.isEmpty()) {
            List<JobApplicantsResponseDTO.JobBasicInfoDTO> emptyJobList = new ArrayList<>();
            return new JobApplicantsResponseDTO(
                    null,
                    new ArrayList<>(),
                    new JobApplicantsResponseDTO.SummaryDTO(0L, 0L, 0L, 0L),
                    emptyJobList
            );
        }

        JobPost mostRecentJob = null;
        Timestamp mostRecentApplicationDate = null;

        for (JobPost job : companyJobs) {
            List<JobApplication> applications = jobApplicationRepository
                    .findByJobIdAndCompanyProfileIdWithStatus(job.getJobId(), companyProfile.getCompanyProfileId(), null);

            if (!applications.isEmpty()) {
                for (JobApplication app : applications) {
                    if (mostRecentApplicationDate == null ||
                        (app.getAppliedDate() != null && app.getAppliedDate().after(mostRecentApplicationDate))) {
                        mostRecentApplicationDate = app.getAppliedDate();
                        mostRecentJob = job;
                    }
                }
            }
        }

        if (mostRecentJob == null) {
            JobPost firstJob = companyJobs.get(0);
            JobApplicantsResponseDTO.JobBasicInfoDTO jobInfo = new JobApplicantsResponseDTO.JobBasicInfoDTO(
                    firstJob.getJobId(), firstJob.getJobTitle(), firstJob.getJobOpeningDate(), firstJob.getApplicationDeadlineDate());

            return new JobApplicantsResponseDTO(
                    jobInfo,
                    new ArrayList<>(),
                    new JobApplicantsResponseDTO.SummaryDTO(0L, 0L, 0L, 0L),
                    new ArrayList<>()
            );
        }

        return getJobApplicants(mostRecentJob.getJobId(), null, null);
    }

    @Transactional(readOnly = true)
    public JobApplicantsResponseDTO getJobApplicants(Long jobId, Integer statusId, Integer timeFilterId) {
        Registereduser user = getUser();

        if (!hasRole(user, "RECRUITER")) {
            throw new IllegalStateException(
                    messageSource.getMessage("msg.role_mismatch", null, Locale.getDefault())
            );
        }

        CompanyProfile companyProfile = registeruserRepository
                .findCompanyProfileByUserId(user.getUserid())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.company_profile_not_found", null, Locale.getDefault())
                ))
                .getCompanyProfile();

        if (companyProfile.getCompanyName() == null || companyProfile.getCompanyName().isEmpty() ||
            companyProfile.getCompanyEmail() == null || companyProfile.getCompanyEmail().isEmpty()) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.company_profile_incomplete", null, Locale.getDefault())
            );
        }

        if (jobId == null) {
            List<JobPost> companyJobs = jobRepository.findByCompanyProfileCompanyProfileId(companyProfile.getCompanyProfileId());

            if (companyJobs.isEmpty()) {
                logger.info("No jobs found for company profile ID: {}", companyProfile.getCompanyProfileId());
                JobApplicantsResponseDTO.JobBasicInfoDTO jobInfo = new JobApplicantsResponseDTO.JobBasicInfoDTO(
                        null, "No jobs available", null, null);

                List<JobApplicantsResponseDTO.JobBasicInfoDTO> emptyJobList = new ArrayList<>();
                return new JobApplicantsResponseDTO(
                        jobInfo,
                        new ArrayList<>(),
                        new JobApplicantsResponseDTO.SummaryDTO(0L, 0L, 0L, 0L),
                        emptyJobList
                );
            }

            Integer timeFilterDays = null;
            Timestamp timeFilterDate = null;
            String timeFilterName = null;

            if (timeFilterId != null) {
                MasterData timeFilterData = masterDataRepository.findByComponentType_IdAndMasterDataId(7, timeFilterId);
                if (timeFilterData != null) {
                    timeFilterName = timeFilterData.getValue();
                    timeFilterDays = calculateDaysFromTimeFilter(timeFilterId);
                    if (timeFilterDays != null) {
                        Calendar cal = Calendar.getInstance();
                        cal.add(Calendar.DAY_OF_MONTH, -timeFilterDays);
                        timeFilterDate = new Timestamp(cal.getTimeInMillis());
                        logger.info("Applying time filter: {} ({}), filtering from date: {}",
                                timeFilterName, timeFilterDays, timeFilterDate);
                    }
                }
            }

            List<JobApplication> allApplications = new ArrayList<>();
            List<JobApplicantsResponseDTO.ApplicantDTO> allApplicantDTOs = new ArrayList<>();
            long totalApplicants = 0;
            long appliedCount = 0;
            long approvedCount = 0;
            long rejectedCount = 0;

            for (JobPost job : companyJobs) {
                List<JobApplication> applications = jobApplicationRepository
                        .findByJobIdAndCompanyProfileIdWithStatusAndTimeFilter(
                            job.getJobId(),
                            companyProfile.getCompanyProfileId(),
                            statusId,
                            timeFilterDate);

                if (!applications.isEmpty()) {
                    allApplications.addAll(applications);

                    for (JobApplication application : applications) {
                        CandidateProfile candidateProfile = application.getCandidateProfile();
                        String name = candidateProfile.getFullName();
                        if (name == null || name.isEmpty()) {
                            Registereduser candidate = candidateProfile.getRegistereduser();
                            name = (candidate.getFirstname() != null ? candidate.getFirstname() : "") + " " +
                                   (candidate.getLastname() != null ? candidate.getLastname() : "");
                            name = name.trim();
                        }

                        String statusValue = "";
                        Integer appStatusId = application.getStatusId();
                        if (appStatusId != null) {
                            MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, appStatusId);
                            if (statusData != null) {
                                statusValue = statusData.getValue();
                            }
                        }

                        totalApplicants++;
                        if (appStatusId != null) {
                            MasterData appStatusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, appStatusId);
                            if (appStatusData != null) {
                                String appStatusValue = appStatusData.getValue();
                                if ("Applied".equals(appStatusValue) || "Shortlisted".equals(appStatusValue) || "Interview".equals(appStatusValue)) {
                                    appliedCount++;
                                } else if ("Hired".equals(appStatusValue)) {
                                    approvedCount++;
                                } else if ("Rejected".equals(appStatusValue)) {
                                    rejectedCount++;
                                }
                            }
                        }

                        Integer salaryCurrencyId = parseMasterDataId(candidateProfile.getSalaryCurrency());
                        Integer experienceId = parseMasterDataId(candidateProfile.getExperience());
                        Integer educationId = parseMasterDataId(candidateProfile.getEducation());
                        Integer jobTypeId = parseMasterDataId(job.getJobType());
                        Integer careerLevelId = parseMasterDataId(job.getCareerLevel());
                        Integer industryId = parseMasterDataId(job.getIndustry());
                        Integer qualificationId = parseMasterDataId(job.getQualification());
                        Integer jobCategoryId = parseMasterDataId(job.getJobCategory());
                        Integer jobSubCategoryId = parseMasterDataId(job.getJobSubCategory());
                        Integer jobSubSubCategoryId = parseMasterDataId(job.getJobSubSubCategory());

                        String salaryCurrencyName = getMasterDataValue(14, salaryCurrencyId);
                        String experienceName = getMasterDataValue(5, experienceId);
                        String educationName = getMasterDataValue(6, educationId);
                        String jobTypeName = getMasterDataValue(2, jobTypeId);
                        String careerLevelName = getMasterDataValue(4, careerLevelId);
                        String industryName = getMasterDataValue(1, industryId);
                        String qualificationName = getMasterDataValue(8, qualificationId);
                        String jobCategoryName = getMasterDataValue(9, jobCategoryId);
                        String jobSubCategoryName = getMasterDataValue(10, jobSubCategoryId);
                        String jobSubSubCategoryName = getMasterDataValue(11, jobSubSubCategoryId);

                        String candidateCategoryName = getMasterDataValue(9, parseMasterDataId(candidateProfile.getJobCategories()));
                        String candidateSubCategoryName = getMasterDataValue(10, parseMasterDataId(candidateProfile.getJobSubCategories()));
                        String candidateSubSubCategoryName = getMasterDataValue(11, parseMasterDataId(candidateProfile.getJobSubSubCategories()));

                        String fullAddress = buildFullAddress(candidateProfile);

                        JobApplicantsResponseDTO.ApplicantDTO applicantDTO = new JobApplicantsResponseDTO.ApplicantDTO(
                                application.getApplicationId(),
                                name,
                                candidateProfile.getLocation(),
                                candidateProfile.getExpectedSalary(),
                                appStatusId,
                                statusValue,
                                candidateProfile.getProfilePictureURL(),
                                candidateProfile.getDesignation(),
                                candidateProfile.getCurrentSalary(),
                                getResumeUrlForApplicant(application, candidateProfile),
                                candidateProfile.getSkills(),
                                candidateProfile.getCandidateProfileId(),
                                salaryCurrencyId,
                                salaryCurrencyName,
                                experienceId,
                                experienceName,
                                educationId,
                                educationName,
                                jobTypeId,
                                jobTypeName,
                                careerLevelId,
                                careerLevelName,
                                industryId,
                                industryName,
                                qualificationId,
                                qualificationName,
                                jobCategoryId,
                                jobCategoryName,
                                jobSubCategoryId,
                                jobSubCategoryName,
                                jobSubSubCategoryId,
                                jobSubSubCategoryName,
                                job.getQuestion(),
                                application.getAnswer(),
                                candidateProfile.getAddressCountry(),
                                candidateProfile.getAddressCity(),
                                application.getAppliedDate(),
                                timeFilterName,
                                fullAddress,
                                candidateProfile.getAddressLineOne(),
                                candidateProfile.getAddressLineTwo(),
                                candidateProfile.getAddressCity(),
                                candidateProfile.getAddressCountry(),
                                candidateProfile.getAddressPincode(),
                                candidateProfile.getExperience(),
                                candidateProfile.getYearsOfExperience(),
                                candidateProfile.getRegistereduser().isHasCandidateProfile(),
                                candidateCategoryName,
                                candidateSubCategoryName,
                                candidateSubSubCategoryName
                        );

                        allApplicantDTOs.add(applicantDTO);
                    }
                }
            }

            if (allApplicantDTOs.isEmpty()) {
                logger.info("No applications found for any jobs with the given filters");
                JobApplicantsResponseDTO.JobBasicInfoDTO jobInfo = new JobApplicantsResponseDTO.JobBasicInfoDTO(
                        null, "No applications found with the given filters", null, null);

                List<JobApplicantsResponseDTO.JobBasicInfoDTO> emptyJobList = new ArrayList<>();
                return new JobApplicantsResponseDTO(
                        jobInfo,
                        new ArrayList<>(),
                        new JobApplicantsResponseDTO.SummaryDTO(0L, 0L, 0L, 0L),
                        emptyJobList
                );
            }

            List<JobApplicantsResponseDTO.JobBasicInfoDTO> jobInfoList = new ArrayList<>();

            Set<Long> jobIdsWithApplicants = new HashSet<>();
            for (JobApplication application : allApplications) {
                if (application.getJobPost() != null) {
                    jobIdsWithApplicants.add(application.getJobPost().getJobId());
                }
            }

            for (JobPost job : companyJobs) {
                if (jobIdsWithApplicants.contains(job.getJobId())) {
                    JobApplicantsResponseDTO.JobBasicInfoDTO jobInfoDTO = new JobApplicantsResponseDTO.JobBasicInfoDTO(
                        job.getJobId(), job.getJobTitle(), job.getJobOpeningDate(), job.getApplicationDeadlineDate());
                    jobInfoList.add(jobInfoDTO);
                }
            }

            JobApplicantsResponseDTO.JobBasicInfoDTO jobInfo = new JobApplicantsResponseDTO.JobBasicInfoDTO(
                null, "All Jobs", null, null);

            JobApplicantsResponseDTO.SummaryDTO summary = new JobApplicantsResponseDTO.SummaryDTO(
                    totalApplicants, appliedCount, approvedCount, rejectedCount);

            logger.info("Retrieved {} applicants across all jobs with filters - statusId: {}, timeFilterId: {}",
                    allApplicantDTOs.size(), statusId, timeFilterId);

            return new JobApplicantsResponseDTO(jobInfo, allApplicantDTOs, summary, jobInfoList);
        }

        JobPost jobPost = jobRepository.findById(jobId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_not_found", null, Locale.getDefault())
                ));

        if (!jobPost.getCompanyProfile().getCompanyProfileId().equals(companyProfile.getCompanyProfileId())) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.job_not_owned", null, Locale.getDefault())
            );
        }

        Integer timeFilterDays = null;
        Timestamp timeFilterDate = null;
        String timeFilterName = null;

        if (timeFilterId != null) {
            MasterData timeFilterData = masterDataRepository.findByComponentType_IdAndMasterDataId(7, timeFilterId);
            if (timeFilterData != null) {
                timeFilterName = timeFilterData.getValue();
                timeFilterDays = calculateDaysFromTimeFilter(timeFilterId);
                if (timeFilterDays != null) {
                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.DAY_OF_MONTH, -timeFilterDays);
                    timeFilterDate = new Timestamp(cal.getTimeInMillis());
                    logger.info("Applying time filter: {} ({}), filtering from date: {}",
                            timeFilterName, timeFilterDays, timeFilterDate);
                }
            }
        }

        List<JobApplication> applications = jobApplicationRepository
                .findByJobIdAndCompanyProfileIdWithStatusAndTimeFilter(jobId, companyProfile.getCompanyProfileId(), statusId, timeFilterDate);

        List<JobApplicantsResponseDTO.JobBasicInfoDTO> jobInfoList = new ArrayList<>();
        JobApplicantsResponseDTO.JobBasicInfoDTO jobInfoDTO = new JobApplicantsResponseDTO.JobBasicInfoDTO(
            jobPost.getJobId(), jobPost.getJobTitle(), jobPost.getJobOpeningDate(), jobPost.getApplicationDeadlineDate());
        jobInfoList.add(jobInfoDTO);

        JobApplicantsResponseDTO response = new JobApplicantsResponseDTO();

        JobApplicantsResponseDTO.JobBasicInfoDTO jobInfo = new JobApplicantsResponseDTO.JobBasicInfoDTO(
                jobPost.getJobId(), jobPost.getJobTitle(), jobPost.getJobOpeningDate(), jobPost.getApplicationDeadlineDate());
        response.setJob(jobInfo);
        response.setJobs(jobInfoList);

        List<JobApplicantsResponseDTO.ApplicantDTO> applicantDTOs = new ArrayList<>();
        for (JobApplication application : applications) {
            CandidateProfile candidateProfile = application.getCandidateProfile();
            String name = candidateProfile.getFullName();
            if (name == null || name.isEmpty()) {
                Registereduser candidate = candidateProfile.getRegistereduser();
                name = (candidate.getFirstname() != null ? candidate.getFirstname() : "") + " " +
                       (candidate.getLastname() != null ? candidate.getLastname() : "");
                name = name.trim();
            }

            String statusValue = "";
            if (application.getStatusId() != null) {
                MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, application.getStatusId());
                if (statusData != null) {
                    statusValue = statusData.getValue();
                }
            }

            Integer salaryCurrencyId = parseMasterDataId(candidateProfile.getSalaryCurrency());
            Integer experienceId = parseMasterDataId(candidateProfile.getExperience());
            Integer educationId = parseMasterDataId(candidateProfile.getEducation());
            Integer jobTypeId = parseMasterDataId(jobPost.getJobType());
            Integer careerLevelId = parseMasterDataId(jobPost.getCareerLevel());
            Integer industryId = parseMasterDataId(jobPost.getIndustry());
            Integer qualificationId = parseMasterDataId(jobPost.getQualification());
            Integer jobCategoryId = parseMasterDataId(jobPost.getJobCategory());
            Integer jobSubCategoryId = parseMasterDataId(jobPost.getJobSubCategory());
            Integer jobSubSubCategoryId = parseMasterDataId(jobPost.getJobSubSubCategory());

            String salaryCurrencyName = getMasterDataValue(candidateProfile.getSalaryCurrency(), 14);
            String experienceName = getMasterDataValue(candidateProfile.getExperience(), 5);
            String educationName = getMasterDataValue(candidateProfile.getEducation(), 8);
            String jobTypeName = getMasterDataValue(jobPost.getJobType(), 13);
            String careerLevelName = getMasterDataValue(jobPost.getCareerLevel(), 4);
            String industryName = getMasterDataValue(jobPost.getIndustry(), 9);
            String qualificationName = getMasterDataValue(jobPost.getQualification(), 8);
            String jobCategoryName = getMasterDataValue(jobPost.getJobCategory(), 12);

            String jobSubCategoryName = null;
            if (jobPost.getJobSubCategory() != null) {
                MasterData subCategoryData = masterDataRepository.findByComponentType_IdAndMasterDataId(17, Integer.parseInt(jobPost.getJobSubCategory()));
                if (subCategoryData != null) {
                    String value = subCategoryData.getValue();
                    if (value != null && value.contains("|")) {
                        jobSubCategoryName = value.split("\\|")[0];
                    } else {
                        jobSubCategoryName = value;
                    }
                }
            }

            String jobSubSubCategoryName = null;
            if (jobPost.getJobSubSubCategory() != null) {
                MasterData subSubCategoryData = masterDataRepository.findByComponentType_IdAndMasterDataId(23, Integer.parseInt(jobPost.getJobSubSubCategory()));
                if (subSubCategoryData != null) {
                    String value = subSubCategoryData.getValue();
                    if (value != null && value.contains("|")) {
                        jobSubSubCategoryName = value.split("\\|")[0];
                    } else {
                        jobSubSubCategoryName = value;
                    }
                }
            }

            String candidateCategoryName = null;
            String candidateSubCategoryName = null;
            String candidateSubSubCategoryName = null;

            if (candidateProfile.getJobCategories() != null && !candidateProfile.getJobCategories().isEmpty()) {
                candidateCategoryName = candidateProfile.getJobCategories();
                if (candidateCategoryName.contains("|")) {
                    candidateCategoryName = candidateCategoryName.split("\\|")[0];
                }
            }

            if (candidateProfile.getJobSubCategories() != null && !candidateProfile.getJobSubCategories().isEmpty()) {
                candidateSubCategoryName = candidateProfile.getJobSubCategories();
                if (candidateSubCategoryName.contains("|")) {
                    candidateSubCategoryName = candidateSubCategoryName.split("\\|")[0];
                }
            }

            if (candidateProfile.getJobSubSubCategories() != null && !candidateProfile.getJobSubSubCategories().isEmpty()) {
                candidateSubSubCategoryName = candidateProfile.getJobSubSubCategories();
                if (candidateSubSubCategoryName.contains("|")) {
                    candidateSubSubCategoryName = candidateSubSubCategoryName.split("\\|")[0];
                }
            }

            String fullAddress = getFullAddress(candidateProfile);

            JobApplicantsResponseDTO.ApplicantDTO applicantDTO = new JobApplicantsResponseDTO.ApplicantDTO(
                    application.getApplicationId(),
                    name,
                    candidateProfile.getLocation(),
                    candidateProfile.getExpectedSalary(),
                    application.getStatusId(),
                    statusValue,
                    candidateProfile.getProfilePictureURL(),
                    candidateProfile.getDesignation(),
                    candidateProfile.getCurrentSalary(),
                    getResumeUrlForApplicant(application, candidateProfile),
                    candidateProfile.getSkills(),
                    candidateProfile.getCandidateProfileId(),
                    salaryCurrencyId,
                    salaryCurrencyName,
                    experienceId,
                    experienceName,
                    educationId,
                    educationName,
                    jobTypeId,
                    jobTypeName,
                    careerLevelId,
                    careerLevelName,
                    industryId,
                    industryName,
                    qualificationId,
                    qualificationName,
                    jobCategoryId,
                    jobCategoryName,
                    jobSubCategoryId,
                    jobSubCategoryName,
                    jobSubSubCategoryId,
                    jobSubSubCategoryName,
                    jobPost.getQuestion(),
                    application.getAnswer(),
                    candidateProfile.getAddressCountry(),
                    candidateProfile.getAddressCity(),
                    application.getAppliedDate(),
                    timeFilterName,
                    fullAddress,
                    candidateProfile.getAddressLineOne(),
                    candidateProfile.getAddressLineTwo(),
                    candidateProfile.getAddressCity(),
                    candidateProfile.getAddressCountry(),
                    candidateProfile.getAddressPincode(),
                    candidateProfile.getExperience(),
                    candidateProfile.getYearsOfExperience(),
                    candidateProfile.getRegistereduser().isHasCandidateProfile(),
                    candidateCategoryName,
                    candidateSubCategoryName,
                    candidateSubSubCategoryName
            );
            applicantDTOs.add(applicantDTO);
        }
        response.setApplicants(applicantDTOs);

        List<JobApplication> allApplications = jobApplicationRepository.findByJobIdAndCompanyProfileIdWithStatus(jobId, companyProfile.getCompanyProfileId(), null);

        if (statusId != null || timeFilterDate != null) {
            JobApplicantsResponseDTO.SummaryDTO summary = getFilteredSummary(applications, allApplications);
            response.setSummary(summary);
        } else {
            JobApplicantsResponseDTO.SummaryDTO summary = getSummary(allApplications);
            response.setSummary(summary);
        }

        logger.info("Retrieved {} applicants for job ID: {} by recruiter ID: {}",
                applications.size(), jobId, user.getUserid());

        return response;
    }

    private JobApplicantsResponseDTO.SummaryDTO getSummary(List<JobApplication> allApplications) {
        long totalCount = allApplications.size();
        long appliedCount = 0;
        long approvedCount = 0;
        long rejectedCount = 0;

        for (JobApplication app : allApplications) {
            Integer statusId = app.getStatusId();
            if (statusId != null) {
                MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, statusId);
                if (statusData != null) {
                    String statusValue = statusData.getValue();
                    if ("Applied".equals(statusValue) || "Shortlisted".equals(statusValue) || "Interview".equals(statusValue)) {
                        appliedCount++;
                    } else if ("Hired".equals(statusValue)) {
                        approvedCount++;
                    } else if ("Rejected".equals(statusValue)) {
                        rejectedCount++;
                    }
                }
            }
        }

        return new JobApplicantsResponseDTO.SummaryDTO(
                totalCount, appliedCount, approvedCount, rejectedCount);
    }

    private JobApplicantsResponseDTO.SummaryDTO getFilteredSummary(List<JobApplication> filteredApplications, List<JobApplication> allApplications) {
        if (filteredApplications.size() == allApplications.size()) {
            return getSummary(allApplications);
        }

        long totalCount = filteredApplications.size();
        long appliedCount = 0;
        long approvedCount = 0;
        long rejectedCount = 0;

        for (JobApplication app : filteredApplications) {
            Integer statusId = app.getStatusId();
            if (statusId != null) {
                MasterData statusData = masterDataRepository.findByComponentType_IdAndMasterDataId(16, statusId);
                if (statusData != null) {
                    String statusValue = statusData.getValue();
                    if ("Applied".equals(statusValue) || "Shortlisted".equals(statusValue) || "Interview".equals(statusValue)) {
                        appliedCount++;
                    } else if ("Hired".equals(statusValue)) {
                        approvedCount++;
                    } else if ("Rejected".equals(statusValue)) {
                        rejectedCount++;
                    }
                }
            }
        }

        return new JobApplicantsResponseDTO.SummaryDTO(
                totalCount, appliedCount, approvedCount, rejectedCount);
    }

    private static String getFullAddress(CandidateProfile candidateProfile) {
        StringBuilder addressBuilder = new StringBuilder();
        if (candidateProfile.getAddressLineOne() != null && !candidateProfile.getAddressLineOne().isEmpty()) {
            addressBuilder.append(candidateProfile.getAddressLineOne());
        }
        if (candidateProfile.getAddressLineTwo() != null && !candidateProfile.getAddressLineTwo().isEmpty()) {
            if (!addressBuilder.isEmpty()) addressBuilder.append(", ");
            addressBuilder.append(candidateProfile.getAddressLineTwo());
        }
        if (candidateProfile.getAddressCity() != null && !candidateProfile.getAddressCity().isEmpty()) {
            if (!addressBuilder.isEmpty()) addressBuilder.append(", ");
            addressBuilder.append(candidateProfile.getAddressCity());
        }
        if (candidateProfile.getAddressCountry() != null && !candidateProfile.getAddressCountry().isEmpty()) {
            if (!addressBuilder.isEmpty()) addressBuilder.append(", ");
            addressBuilder.append(candidateProfile.getAddressCountry());
        }
        if (candidateProfile.getAddressPincode() != null && !candidateProfile.getAddressPincode().isEmpty()) {
            if (!addressBuilder.isEmpty()) addressBuilder.append(" - ");
            addressBuilder.append(candidateProfile.getAddressPincode());
        }

        return !addressBuilder.isEmpty() ? addressBuilder.toString() : null;
    }

    private String buildFullAddress(CandidateProfile candidateProfile) {
        return getFullAddress(candidateProfile);
    }

    private String getMasterDataValue(Integer componentTypeId, Integer masterDataId) {
        if (componentTypeId == null || masterDataId == null) {
            return null;
        }
        MasterData masterData = masterDataRepository.findByComponentType_IdAndMasterDataId(componentTypeId, masterDataId);
        return masterData != null ? masterData.getValue() : null;
    }
}
