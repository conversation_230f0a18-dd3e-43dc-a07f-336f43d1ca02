package com.job.jobportal.controller;


import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.AccountDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AccountDetailsController {

    private static final Logger logger = LoggerFactory.getLogger(AccountDetailsController.class);
    @Autowired
    AccountDetailsService accountDetailsService;
    @Autowired
    MessageSource message;

    @GetMapping("account")
    public ResponseEntity<?> getAccountDetails() throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, accountDetailsService.getAccountDetails(),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PutMapping("account/active")
    public ResponseEntity<?> updateUserAccountActive(@Param("username") String username, @Param("isActive") int isActive) throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, accountDetailsService.blockUser(isActive, username),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PutMapping("account/premium")
    public ResponseEntity<?> updateUserPremium(@Param("isPremiumAccount") int isPremiumAccount) throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, accountDetailsService.premiumUser(isPremiumAccount),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("account/delete")
    public ResponseEntity<?> scheduleDelete(@Param("isDeleteScheduled") int isDeleteScheduled) throws Exception {
        try {
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, accountDetailsService.deleteAccount(isDeleteScheduled),
                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
