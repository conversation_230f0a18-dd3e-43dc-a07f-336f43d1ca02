package com.job.jobportal.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class MarketingUserDTO {

    private String userName;

    private String mobileNo;

    private String emailId;

    private Timestamp createdOn;

    private int isActive;

    private String userType; // "USER" or "SUBSCRIBER"

    public MarketingUserDTO() {
        this.userType = "USER";
    }

    public MarketingUserDTO(String emailId, Timestamp createdOn, String userType) {
        this.emailId = emailId;
        this.createdOn = createdOn;
        this.userType = userType;
        this.isActive = 1;
    }
}
