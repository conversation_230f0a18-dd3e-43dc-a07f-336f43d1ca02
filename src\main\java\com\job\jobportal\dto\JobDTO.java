package com.job.jobportal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
public class JobDTO {
    private Long jobId;
    private String jobTitle;
    private JsonNode jobDescription;
    private JsonNode responsibilitiesAndBenefits;
    private String departmentName;
    private Integer departmentId;
    private Long numberOfPositions;
    private String location;
    private Integer locationId;
    private String contactEmail;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp postedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp updatedDate;
    private String status;
    private Long companyProfileId;
    private Long userId;
    private String username;
    private List<Integer> specialisms;
    private List<String> specialismsNames;
    private String retail;
    private String gender;
    private String jobType;
    private Integer jobTypeId;
    private String careerLevel;
    private Integer careerLevelId;
    private String experience;
    private Integer experienceId;
    private String industry;
    private Integer industryId;
    private String qualification;
    private Integer qualificationId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp jobOpeningDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp applicationDeadlineDate;

    private String country;
    private String city;
    private String completeAddress;
    private Double jobAddressMapLocationLattitude;
    private Double jobAddressMapLocationLongtitude;

    private String salaryCurrency;
    private Integer salaryCurrencyId;
    private String payType;
    private Integer payTypeId;
    private Long minSalary;
    private Long maxSalary;
    private Boolean hideCompensation;

    private Long companyActiveJobCount;

    private String companyName;
    private String companyWebsite;
    private String companyLogoURL;
    private String companyAddressCity;
    private String companyAddressState;
    private String companyAddressCountry;
    private Double companyAddressMapLocationLattitude;
    private Double companyAddressMapLocationLongtitude;
    private String companyEstYear;
    private String companyPhoneNumber;
    private String companySocialLinkedIn;
    private String companySocialFacebook;
    private String companySocialTwitter;
    private String companySocialGlassDoor;
    private String pincode;
    private int companyTeamSize;
    private int companyNatureOfBusiness;
    private Integer jobCategory;
    private String jobCategoryName;
    private Integer jobSubCategory;
    private String jobSubCategoryName;
    private Integer jobSubSubCategory;
    private String jobSubSubCategoryName;
    private String district;
    private String question;
    private String state;
    private List<String> keywords;

    private ApplicationSummaryDTO applicationSummary;
}
