package com.job.jobportal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.model.JobApplication;
import com.job.jobportal.model.JobPost;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
public class JobApplicationResponseDTO {
    private Long applicationId;
    private Long candidateProfileId;
    private Long companyProfileId;
    private Long jobPostId;
    private Integer statusId;
    private String statusValue;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp appliedDate;

    private Long companyActiveJobCount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp updatedDate;
    private String jobTitle;
    private String jobDescription;
    private String location;
    private Long minSalary;
    private Long maxSalary;
    private String jobType;
    private String careerLevel;
    private String experience;
    private String industry;
    private String qualification;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp postedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Timestamp applicationDeadlineDate;
    private String statusJob;
    private String companyName;
    private String companyEmail;
    private String companyPhoneNumber;
    private String companyWebsite;
    private String companyAddressCity;
    private String companyAddressCountry;
    private String companyAddressComplete;
    private String state;
    private String pincode;

    public JobApplicationResponseDTO(JobApplication jobApplication, JobPost jobPost) {
        this.applicationId = jobApplication.getApplicationId();
        this.candidateProfileId = jobApplication.getCandidateProfile().getCandidateProfileId();
        this.companyProfileId = jobApplication.getCompanyProfile().getCompanyProfileId();
        this.jobPostId = jobApplication.getJobPost().getJobId();
        this.statusId = jobApplication.getStatusId();
        this.appliedDate = jobApplication.getAppliedDate();
        this.updatedDate = jobApplication.getUpdatedDate();
        this.jobTitle = jobPost.getJobTitle();
        this.jobDescription = jobPost.getJobDescription() != null ? jobPost.getJobDescription().toString() : null;
        this.location = jobPost.getLocation();
        this.minSalary = jobPost.getMinSalary();
        this.maxSalary = jobPost.getMaxSalary();
        this.jobType = jobPost.getJobType();
        this.careerLevel = jobPost.getCareerLevel();
        this.experience = jobPost.getExperience();
        this.industry = jobPost.getIndustry();
        this.qualification = jobPost.getQualification();
        this.postedDate = jobPost.getPostedDate();
        this.applicationDeadlineDate = jobPost.getApplicationDeadlineDate();
        this.statusJob = jobPost.getStatus().toString();
        this.state = jobPost.getState();
        this.pincode = jobPost.getPincode();

        CompanyProfile company = jobPost.getCompanyProfile();
        this.companyName = company.getCompanyName();
        this.companyEmail = company.getCompanyEmail();
        this.companyPhoneNumber = company.getCompanyPhoneNumber();
        this.companyWebsite = company.getCompanyWebsite();
        this.companyAddressCity = company.getCompanyAddressCity();
        this.companyAddressCountry = company.getCompanyAddressCountry();
        String addressLineTwo = company.getCompanyAddressLineTwo();
        this.companyAddressComplete = company.getCompanyAddressLineOne() + (addressLineTwo != null ? ", " + addressLineTwo : "");
    }
}