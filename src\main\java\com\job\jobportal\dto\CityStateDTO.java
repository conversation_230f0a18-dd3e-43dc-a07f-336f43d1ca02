package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CityStateDTO {
    private String city;
    private String state;
    private String district;

    public CityStateDTO(String city, String state) {
        this.city = city;
        this.state = state;
    }

    public CityStateDTO(String city, String state, String district) {
        this.city = city;
        this.state = state;
        this.district = district;
    }
}
