package com.job.jobportal.security;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Roles;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.util.ConstantsUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.util.*;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
public class UserPrincipal implements OAuth2User, UserDetails {
    private Long id;
    private String email;
    private String userName;
    private String password;
    private int subscriptionPlanType;
    private int subscriptionStatus;
    private AuthProvider clientType;
    private String permissions;

    private int jobPostsLimit;
    private int jobPostsRemaining;
    private Date jobPostsResetDate;

    private Long paymentMethodId;
    private String paymentMethodName;

    private Set<Roles> roles;

    private boolean hasCompanyProfileId;
    private boolean hasCandidateProfile;
    private int hasPassword;

    private Collection<? extends GrantedAuthority> authorities;
    private Map<String, Object> attributes;

    public UserPrincipal(Long id, String email, String password, String userName, AuthProvider clientType,
                     Collection<? extends GrantedAuthority> authorities, Set<Roles> rolesIp,
                     boolean hasCompanyProfileId, boolean hasCandidateProfile, int hasPassword,
                     int subscriptionPlanType, int subscriptionStatus, String permissions,
                     int jobPostsLimit, int jobPostsRemaining, Date jobPostsResetDate,Long paymentMethodId, String paymentMethodName) {
        this.id = id;
        this.email = email;
        this.userName = userName;
        this.password = password;
        this.clientType = clientType;
        this.authorities = authorities;
        this.roles = rolesIp;
        this.hasCompanyProfileId = hasCompanyProfileId;
        this.hasCandidateProfile = hasCandidateProfile;
        this.hasPassword = hasPassword;
        this.subscriptionPlanType = subscriptionPlanType;
        this.subscriptionStatus = subscriptionStatus;
        this.permissions = permissions;
        this.jobPostsLimit = jobPostsLimit;
        this.jobPostsRemaining = jobPostsRemaining;
        this.jobPostsResetDate = jobPostsResetDate;
        this.paymentMethodId = paymentMethodId;
        this.paymentMethodName = paymentMethodName;
    }


    public static UserPrincipal create(Registereduser user, Subscription subscription) {
        return create(user, subscription, null, null);
    }

    public static UserPrincipal create(Registereduser user, Subscription subscription, Long paymentMethodId,
            String paymentMethodName) {
        List<GrantedAuthority> authorities = Collections
                .singletonList(new SimpleGrantedAuthority(user.getRoles().stream().findFirst().get().getRolename()));
        if (subscription == null) {
            subscription = new Subscription();
            subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_NOT_CREATED);
            subscription.setSubscriptionPlanType(0);
            subscription.setPermissions(ConstantsUtil.DEFAULT_FREE_PLAN_PERMISSIONS);
            subscription.setJobPostsLimit(0);
            subscription.setJobPostsRemaining(0);
            subscription.setJobPostsResetDate(null);
        }
        return new UserPrincipal(
                user.getUserid(),
                user.getEmail(),
                user.getPassword(),
                user.getUsername(),
                user.getProvider(),
                authorities,
                user.getRoles(),
                user.isHasCompanyProfileId(),
                user.isHasCandidateProfile(),
                user.getHasPassword(),
                subscription.getSubscriptionPlanType(),
                subscription.getSubscriptionStatus(),
                subscription.getPermissions(),
                subscription.getJobPostsLimit(),
                subscription.getJobPostsRemaining(),
                subscription.getJobPostsResetDate(),
                paymentMethodId,
                paymentMethodName);
    }

    public static UserPrincipal create(Registereduser user, Map<String, Object> attributes, Subscription subscription) {
        UserPrincipal userPrincipal = UserPrincipal.create(user, subscription);
        userPrincipal.setAttributes(attributes);
        return userPrincipal;
    }

    public static UserPrincipal create(Registereduser user, Map<String, Object> attributes, Subscription subscription,
                                       Long paymentMethodId, String paymentMethodName) {
        UserPrincipal userPrincipal = UserPrincipal.create(user, subscription, paymentMethodId, paymentMethodName);
        userPrincipal.setAttributes(attributes);
        return userPrincipal;
    }

    public Long getId() {
        return id;
    }

    public String getEmail() {
        return email;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return email;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {

        if (null == roles) {
            return Collections.emptySet();
        }
        Set<SimpleGrantedAuthority> authorities = new HashSet<>();
        roles.forEach(role -> {
            authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getRolename()));
        });
        return authorities;
    }

    @Override
    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    @Override
    public String getName() {
        return String.valueOf(id);
    }

    public AuthProvider getClientType() {
        return clientType;
    }


    public int getHasPassword() {
        return hasPassword;
    }

    public boolean getHasCompanyProfileId() {
        return this.hasCompanyProfileId;
    }

    public boolean getHasCandidateProfile() {
        return this.hasCandidateProfile;
    }
}
