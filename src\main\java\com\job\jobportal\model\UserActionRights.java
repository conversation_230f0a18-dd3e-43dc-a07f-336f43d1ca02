package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
public class UserActionRights {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long actionId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userid")
    private Registereduser  registereduser ;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "roleid")
    private Roles   role ;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "componentsId")
    private Components    components;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "permissionsId")
    private Permissions   permissions;

    private int enabled;

}
