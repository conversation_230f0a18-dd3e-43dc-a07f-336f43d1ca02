package com.job.jobportal.dto;

import com.job.jobportal.model.Profile;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class InternalNotificationDTO {

    private Long notificationId;

    private String notificationTitle;

    private String notificationDescription;

    private Timestamp notificationDate;

    private int notificationType;

    private Profile profile;

    private Long dairyNotesId;
}
