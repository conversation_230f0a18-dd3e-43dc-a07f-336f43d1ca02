package com.job.jobportal.repository;

import com.job.jobportal.model.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {
    Optional<Permission> findByPermissionCode(String permissionCode);

    List<Permission> findBySubscriptionPlanType(Integer subscriptionPlanType);

    List<Permission> findBySubscriptionPlanTypeIn(List<Integer> subscriptionPlanTypes);
}
