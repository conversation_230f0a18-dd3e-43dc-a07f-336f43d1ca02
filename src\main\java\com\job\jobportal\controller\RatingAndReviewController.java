package com.job.jobportal.controller;

import com.job.jobportal.dto.RatingAndReviewDTO;
import com.job.jobportal.model.RatingAndReview;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.RatingAndReviewService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class RatingAndReviewController {

    @Autowired
    RatingAndReviewService ratingAndReviewService;

    private static final Logger logger = LoggerFactory.getLogger(RatingAndReviewController.class);


    @GetMapping("/ratingandreview/{courseId}")
    public ResponseEntity<?> getRatingAndReview(@PathVariable("courseId") Long courseId) {
        List<RatingAndReview> li = ratingAndReviewService.getAllRating(courseId);
        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, li,
                "msg.rating_review_get_success"), HttpStatus.OK);

    }

    @GetMapping("/ratingandreview")
    public ResponseEntity<?> getAllRatingAndReview() {
        List<RatingAndReview> li = ratingAndReviewService.getAllRating();
        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, li,
                "msg.rating_review_get_success"), HttpStatus.OK);

    }

    @DeleteMapping("/ratingandreview/{ratingId}")
    public ResponseEntity<?> deleteRatingAndReview(@PathVariable("ratingId") Long ratingId) {
        ratingAndReviewService.deleteRating(ratingId);
        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                "msg.rating_review_delete_success"), HttpStatus.OK);

    }

    @PostMapping("/ratingandreview")
    public ResponseEntity<?> addRatingAndReview(@RequestBody RatingAndReviewDTO ratingAndReviewDTO) {
        try {
            RatingAndReview li = ratingAndReviewService.addRating(ratingAndReviewDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, li,
                    "msg.rating_review_save_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }


}
