spring.jpa.hibernate.ddl-auto=update
#spring.jpa.hibernate.ddl-auto=create-drop
#spring.jpa.hibernate.ddl-auto=create
#spring.jpa.hibernate.ddl-auto=none
spring.datasource.url=*************************************
spring.datasource.username=root
spring.datasource.password=system123#
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=true
jwt.secret=jobportalapp
spring.jpa.properties.hibernate.jdbc.time_zone=UTC
application.baseUrl=http://localhost:8080
# application.baseFrontendUrl=http://app.localhost:3000
application.baseFrontendUrl=http://localhost:3000

management.endpoints.web.exposure.include=health,info,metrics
# Optionally, configure access to the health endpoint
management.endpoint.health.probe.enabled=true
management.endpoints.web.base-path=/actuator
management.endpoint.health.show-details=always
application.ticketEmail=<EMAIL>
application.certificatecode=JB
application.name=Job Portal
application.description=Portal for Applying Jobs under several department
application.version=1.0.0
app.base-url=https://www.groglojobs.co.uk
server.port=8080
spring.sql.init.mode=embedded
#spring.sql.init.mode=never
app.database.initialize=true
spring.jpa.open-in-view=false
spring.jpa.defer-datasource-initialization=true
#spring.flyway.enabled=true
#spring.jpa.defer-datasource-initialization=false
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1
application.aws.bucketname=ebrainyvideostreaming
application.aws.import_excel=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com
application.aws.cloudfronts3url=https://d19w1vowz8zr6e.cloudfront.net
application.aws.accessKey=********************
application.aws.secretKey=70ocLBJPVsJaNYEAwu3Pih1Dl3his8/lwztR5qYM
application.aws.region=eu-north-1
application.aws.secretName=stripe

application.email=<EMAIL>
#email.domain.from=<EMAIL>

# Social media links
social.facebook.url=https://www.facebook.com/groglojobs
social.linkedin.url=https://www.linkedin.com/company/groglojobs
social.twitter.url=https://twitter.com/groglojobs

# Social media icons
social.icon.facebook=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png
social.icon.linkedin=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png
social.icon.twitter=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png


#mail
email.provider=domain

# gmail
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=aoamtqqqspnwjuvu
#domain
#email.domain.host=smtp.zoho.in
#email.domain.port=465
#email.domain.username=<EMAIL>
#email.domain.password=rfxVXje1NXUz
#email.domain.from=<EMAIL>
email.domain.host=smtp.zoho.eu
email.domain.port=465
email.domain.username=<EMAIL>
email.domain.password=as2zDmYnQM8Q
email.domain.from=<EMAIL>



spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

#sms
sms.PHONE_NUMBER=+16203901757
#payment
# Razorpay
stripe.webhook.signing.currency=GBP
application.multiCurrency=false
application.multiCurrencyList=USD,GBP,EUR
#security
spring.security.oauth2.client.registration.google.clientId=10890670190-6pmq4d6q07fmf9cvcm03ktnod290oi32.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.scope=email, profile
spring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}
spring.security.oauth2.client.registration.facebook.clientId=1456102035268496
spring.security.oauth2.client.registration.facebook.clientSecret=********************************
spring.security.oauth2.client.registration.facebook.scope=email, public_profile
spring.security.oauth2.client.registration.facebook.redirectUri={baseUrl}/oauth2/callback/{registrationId}
spring.security.oauth2.client.provider.facebook.authorizationUri=https://www.facebook.com/v3.0/dialog/oauth
spring.security.oauth2.client.provider.facebook.tokenUri=https://graph.facebook.com/v3.0/oauth/access_token
#spring.security.oauth2.client.provider.facebook.userInfoUri=https://graph.facebook.com/v3.0/me?fields=id,first_name,middle_name,last_name,name,email,verified,is_verified
app.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1
app.auth.tokenExpirationMsec=864000000
app.cors.allowedOrigins=http://localhost:3000,http://localhost:8080,http://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,https://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,*
app.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect
#logs
logging.level.root=INFO
logging.config=classpath:logback-spring.xml
logging.level.org.springframework=INFO
logging.level.org.springframework.boot=INFO
logging.level.org.springframework.boot.autoconfigure=INFO
logging.level.org.springframework.boot.context=INFO
logging.level.org.springframework.boot.devtools=INFO
logging.level.org.springframework.web=INFO
spring.devtools.restart.enabled=false

# stripe
stripe.api.key=sk_test_51ROBqQPImHquQ8udrDgxqFEuPug1jrX4DUYyUckEyNIyXXeGK7C7EI2wMYhVGzTYcYcB4HeQpYxBIQQq92C6jwHq00pCJcTPb3
stripe.webhook.signing.secret=whsec_UdgEwfjn6WRik5MO5OadloreZyHjUyZk
stripe.price.standard.monthly=price_1RODXkPImHquQ8udb9ZtN7cR
stripe.price.standard.yearly=price_1RODpmPImHquQ8udnYE8iHHz
stripe.price.premium.monthly=price_1RODaWPImHquQ8udXW3EoAkW
stripe.price.premium.yearly=price_1RODqYPImHquQ8udiCh7r6eX
stripe.price.enterprise.monthly=price_1RODcFPImHquQ8udokaJ8Wl8
stripe.price.enterprise.yearly=price_1RODrbPImHquQ8uds6NiF1wj



