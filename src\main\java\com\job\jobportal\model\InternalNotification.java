package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class InternalNotification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long notificationId;

    private String notificationTitle;

    @Column(columnDefinition = "LONGTEXT")
    private String notificationDescription;

    private Timestamp notificationDate;

    private int notificationType;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "profileId")
    private Profile profile;

    private Long dairyNotesId;
}
