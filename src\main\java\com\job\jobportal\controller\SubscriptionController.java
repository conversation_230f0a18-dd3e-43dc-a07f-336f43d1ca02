package com.job.jobportal.controller;

import com.job.jobportal.dto.SubscriptionDTO;
import com.job.jobportal.model.SubscriptionPlan;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.SubscriptionService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
public class SubscriptionController {

    @Autowired
    SubscriptionService subscriptionService;

    @Autowired
    MessageSource message;

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionController.class);

    @PostMapping("/payment-subscription")
    public ResponseEntity<?> addSubscriptionMethod(@Param("planName") String planName, @Param("isYearly") int isYearly, @Param("isWebsite") int isWebsite) {
        try {
            Map<String, Object> url = subscriptionService.addSubscription(planName);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, url,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }


    @PostMapping("/customer-portal")
    public ResponseEntity<?> customerPortal(@Param("type") int type, @RequestParam(name = "planName", required = false) String planName, @RequestParam(name = "isYearly", required = false, defaultValue = "0") int isYearly) {
        try {
            Map<String, Object> url = subscriptionService.customerPortal();
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, url,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }


    @PostMapping("/stripe_webhooks")
    public ResponseEntity<?> stripe_webhooks(@RequestBody String payload, HttpServletRequest request, HttpServletResponse response) {
        try {
            String sigHeader = request.getHeader("Stripe-Signature");
           // subscriptionService.handleStripeWebhook(payload, sigHeader, response);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PostMapping("/subscription-plan")
    public ResponseEntity<?> addSubscriptionPlan(@RequestBody List<SubscriptionPlan> subscriptionPlans) {
        try {
            subscriptionService.addPlan(subscriptionPlans);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("/subscription/addon")
    public ResponseEntity<?> addonSubscription() {
        try {
            //   subscriptionService.addOnSubscription();
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @GetMapping("/subscription/account")
    public ResponseEntity<?> getSubscriptionDetails() {
        try {
            //subscriptionService.getSubscriptionDetailsBasedOnAccount()
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @GetMapping("/subscription/plan")
    public ResponseEntity<?> getAllSubscriptionDetails() {
        try {
           // subscriptionService.getAllSubscriptionDetails()
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    null), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, message.getMessage("msg.something_went_wrong", null, LocaleContextHolder.getLocale())), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }


}
