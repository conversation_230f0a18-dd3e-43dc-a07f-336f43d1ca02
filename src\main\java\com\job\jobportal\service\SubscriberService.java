package com.job.jobportal.service;

import com.job.jobportal.dto.SubscriberDTO;
import com.job.jobportal.model.Subscriber;
import com.job.jobportal.repository.SubscriberRepository;
import com.job.jobportal.response.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SubscriberService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriberService.class);

    @Autowired
    private SubscriberRepository subscriberRepository;

    public Subscriber saveSubscriber(SubscriberDTO subscriberDTO) throws Exception {
        try {
            if (subscriberRepository.existsByEmail(subscriberDTO.getEmail())) {
                throw new BadRequestException("Email already exists as subscriber");
            }

            Subscriber subscriber = new Subscriber();
            subscriber.setEmail(subscriberDTO.getEmail());
            subscriber.setIsSubscriber(subscriberDTO.getIsSubscriber());

            return subscriberRepository.save(subscriber);
        } catch (BadRequestException e) {
            logger.error("Bad request while saving subscriber: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error saving subscriber: " + e.getMessage());
            throw new Exception("Failed to save subscriber");
        }
    }

    public List<Subscriber> getAllSubscribers() {
        try {
            return subscriberRepository.findAll();
        } catch (Exception e) {
            logger.error("Error getting all subscribers: " + e.getMessage());
            throw e;
        }
    }

    public List<Subscriber> getActiveSubscribers() {
        try {
            return subscriberRepository.findByIsSubscriber(true);
        } catch (Exception e) {
            logger.error("Error getting active subscribers: " + e.getMessage());
            throw e;
        }
    }

    public Page<Subscriber> getActiveSubscribersPaginated(Pageable pageable) {
        try {
            return subscriberRepository.findByIsSubscriberPaginated(true, pageable);
        } catch (Exception e) {
            logger.error("Error getting active subscribers paginated: " + e.getMessage());
            throw e;
        }
    }
}
