//package com.job.jobportal.controller;
//
//import com.job.jobportal.response.ApiResponse;
//import com.job.jobportal.response.BadRequestException;
//import com.job.jobportal.service.OtpService;
//import org.springframework.context.MessageSource;
//import org.springframework.context.i18n.LocaleContextHolder;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//@RestController
//@RequestMapping("/otp")
//public class OtpController {
//
//    @Autowired
//    private OtpService otpService;
//
//    @Autowired
//    MessageSource message;
//
//
//   private static final Logger logger = LoggerFactory.getLogger(OtpController.class);
//
//    @PostMapping("/send")
//    public ResponseEntity<?> sendOtp(@RequestParam String mobileNumber) {
//       try {
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, otpService.sendOtp(mobileNumber),
//                    message.getMessage("msg.otp_sent_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
//        } catch (BadRequestException e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
//
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
//
//   @PostMapping("/verify")
//    public ResponseEntity<?> verifyOtp(@RequestParam String mobileNumber, @RequestParam String otp) {
//       try {
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true,otpService.verifyOtp(mobileNumber, otp),
//                    message.getMessage("msg.otp_verify_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
//        } catch (BadRequestException e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
//
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
//
//     @PostMapping("/resend")
//    public ResponseEntity<?> resendOtp(@RequestParam String mobileNumber) {
//       try {
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true,otpService.resendOtp(mobileNumber),
//                    message.getMessage("msg.otp_resend_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
//        } catch (BadRequestException e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
//
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
//
//    @PostMapping("/widget/verify")
//    public ResponseEntity<?> verifyWidgetOTP(@RequestParam String token) {
//        try {
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true,otpService.verifyWidgetOtp(token),
//                    message.getMessage("msg.otp_verify_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
//        } catch (BadRequestException e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
//
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
//
//
//}
