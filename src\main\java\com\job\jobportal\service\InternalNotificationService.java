package com.job.jobportal.service;

import com.job.jobportal.dto.InternalNotificationDTO;
import com.job.jobportal.model.InternalNotification;
import com.job.jobportal.repository.InternalNotificationRepo;
import com.job.jobportal.util.CommonUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InternalNotificationService {

    @Autowired
    InternalNotificationRepo notificationRepo;

    @Autowired
    ModelMapper modelMapper;


    public List<InternalNotification> getAllNotification(){
        Long userId= CommonUtils.getUserPrincipal().getId();
        //return notificationRepo.findNotificationByUserId(userId);
        return null;
    }

    public InternalNotification addNotification(InternalNotificationDTO internalNotificationDTO){
        InternalNotification notification = modelMapper.map(internalNotificationDTO, InternalNotification.class);
        return notificationRepo.save(notification);
    }


}
