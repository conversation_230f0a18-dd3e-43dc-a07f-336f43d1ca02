package com.job.jobportal.response;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;


/**
 * Created by r<PERSON><PERSON><PERSON><PERSON><PERSON> on 02/08/17.
 */

@Getter
@Setter
public class SignUpRequest {
    @NotNull
    @NotEmpty
    private String firstName;

    private String lastName;

    @NotBlank
    @Email
    private String email;

    @NotBlank
    private String password;

    private String roleName;

    private String mobileNo;

}
