package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Entity
@Getter
@Setter
public class Subscription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long subscriptionId;

    @OneToOne()
    @JoinColumn(name = "userId")
    private Registereduser registereduser;

    private String subscriptionAccountId;

    private int subscriptionPlanType;

    private int subscriptionStatus;

    private String lastPaymentStatus;

    private String permissions;

    @Temporal(TemporalType.TIMESTAMP)
    private Date lastPaymentDate;

    @Temporal(TemporalType.TIMESTAMP)
    private Date trialStartDate;

    @Temporal(TemporalType.TIMESTAMP)
    private Date trialEndDate;

    private boolean trialUsed = false;

    private int jobPostsLimit;

    private int jobPostsRemaining;

    @Temporal(TemporalType.TIMESTAMP)
    private Date jobPostsResetDate;
}
