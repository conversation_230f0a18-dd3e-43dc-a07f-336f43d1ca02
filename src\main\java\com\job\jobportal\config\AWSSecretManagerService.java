package com.job.jobportal.config;

import com.job.jobportal.util.AWSSecretContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AWSSecretManagerService {
    private final AWSSecretContext awsSecretContext;

    @Autowired
    public AWSSecretManagerService(AWSSecretContext awsSecretContext) {
        this.awsSecretContext = awsSecretContext;
    }

    public String getStripeKey() {
        return awsSecretContext.getStripeKey();
    }

    public void setStripeKey(String stripeKey) {
        awsSecretContext.setStripeKey(stripeKey);
    }


    public void setStripeSecret(String stripeSecret) {
        awsSecretContext.setStripeSecret(stripeSecret);
    }


    public void setWebhookSecret(String webhookSecret) {
        awsSecretContext.setWebhookSecret(webhookSecret);
    }

    public String getTwilioAccountId() {
        return awsSecretContext.getTwilioAccountId();
    }

    public void setTwilioAccountId(String twilioAccountId) {
        awsSecretContext.setTwilioAccountId(twilioAccountId);
    }


    public String getTwilioToken() {
        return awsSecretContext.getTwilioToken();
    }

    public void setTwilioToken(String twilioToken) {
        awsSecretContext.setTwilioToken(twilioToken);
    }
    
    public String getWebhookSecret() {
        return awsSecretContext.getWebhookSecret();
    }

}
