package com.job.jobportal.repository;

import com.job.jobportal.dto.JobApplicationFullDTO;
import com.job.jobportal.model.*;

import java.sql.Timestamp;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface JobApplicationRepository extends JpaRepository<JobApplication, Long> {

    long countByJobPostJobId(Long jobId);

    boolean existsByCandidateProfileRegistereduserAndJobPostJobId(Registereduser candidate, Long jobPostId);

    @Query("SELECT ja FROM JobApplication ja " +
            "LEFT JOIN FETCH ja.candidateProfile " +
            "LEFT JOIN FETCH ja.companyProfile " +
            "WHERE ja.applicationId = :applicationId")
    Optional<JobApplication> findByIdWithAssociations(@Param("applicationId") Long applicationId);


    @Query("SELECT ja FROM JobApplication ja " +
            "LEFT JOIN FETCH ja.jobPost jp " +
            "LEFT JOIN FETCH ja.companyProfile " +
            "WHERE ja.candidateProfile.registereduser = :user " +
            "AND (:jobTitle IS NULL OR LOWER(jp.jobTitle) LIKE LOWER(CONCAT('%', :jobTitle, '%'))) " +
            "AND (:statusId IS NULL OR ja.statusIdStr = CAST(:statusId AS string)) " +
            "AND (:timeFilterDays IS NULL OR ja.appliedDate >= :timeFilterDate)")
    Page<JobApplication> findByCandidateProfileRegistereduserAndFilters(
            @Param("user") Registereduser user,
            @Param("jobTitle") String jobTitle,
            @Param("statusId") Integer statusId,
            @Param("timeFilterDays") Integer timeFilterDays,
            @Param("timeFilterDate") Timestamp timeFilterDate,
            Pageable pageable
    );
    /*
        @Query("SELECT ja FROM JobApplication ja " +
                "LEFT JOIN FETCH ja.jobPost " +
                "WHERE ja.jobPost.companyProfile = :companyProfile " +
                "AND (:search IS NULL OR LOWER(ja.jobPost.jobTitle) LIKE LOWER(CONCAT('%', :search, '%'))) " +
                "AND (:status IS NULL OR ja.status = :status)")
        Page<JobApplication> findByJobPostCompanyProfileAndFilters(
                @Param("companyProfile") CompanyProfile companyProfile,
                @Param("search") String search,
                @Param("status") ApplicationStatus status,
                Pageable pageable);

       */
    // For Recruiters
    @Query("SELECT new com.job.jobportal.dto.JobApplicationFullDTO(" +
            "ja.applicationId, cp.candidateProfileId, comp.companyProfileId, jp.jobId, " +
            "jp.jobTitle, jp.industry, jp.location, comp.companyName, comp.companyLogoURL, " +
            "CAST(ja.statusIdStr AS int), ja.appliedDate, ja.updatedDate, ru.username, " +
            "cp.designation, cp.currentSalary, cp.location, cp.skills, ja.resumeKey, " +
            "jp.jobCategory, null, jp.jobSubCategory, null, jp.jobSubSubCategory, null, jp.completeAddress) " +
            "FROM JobApplication ja " +
            "JOIN ja.jobPost jp " +
            "JOIN jp.companyProfile comp " +
            "JOIN ja.candidateProfile cp " +
            "JOIN cp.registereduser ru " +
            "WHERE comp = :companyProfile " +
            "AND (:jobTitle IS NULL OR LOWER(TRIM(jp.jobTitle)) LIKE LOWER(CONCAT('%', TRIM(:jobTitle), '%'))) " +
            "AND (:companyName IS NULL OR LOWER(TRIM(comp.companyName)) LIKE LOWER(CONCAT('%', TRIM(:companyName), '%'))) " +
            "AND (:statusId IS NULL OR ja.statusIdStr = CAST(:statusId AS string)) " +
            "AND (:timeFilterDays IS NULL OR ja.appliedDate >= :timeFilterDate)")
    Page<JobApplicationFullDTO> findByJobPostCompanyProfileAndFilters(
            @Param("companyProfile") CompanyProfile companyProfile,
            @Param("jobTitle") String jobTitle,
            @Param("companyName") String companyName,
            @Param("statusId") Integer statusId,
            @Param("timeFilterDays") Integer timeFilterDays,
            @Param("timeFilterDate") Timestamp timeFilterDate,
            Pageable pageable
    );

    @Query("SELECT ja FROM JobApplication ja " +
            "JOIN FETCH ja.candidateProfile cp " +
            "JOIN FETCH cp.registereduser ru " +
            "JOIN FETCH ja.jobPost jp " +
            "JOIN FETCH ja.companyProfile " +
            "WHERE jp.jobId = :jobId " +
            "AND jp.companyProfile.companyProfileId = :companyProfileId " +
            "AND (:statusId IS NULL OR ja.statusIdStr = CAST(:statusId AS string))")
    List<JobApplication> findByJobIdAndCompanyProfileIdWithStatus(
            @Param("jobId") Long jobId,
            @Param("companyProfileId") Long companyProfileId,
            @Param("statusId") Integer statusId
    );

    @Query("SELECT ja FROM JobApplication ja " +
            "JOIN FETCH ja.candidateProfile cp " +
            "JOIN FETCH cp.registereduser ru " +
            "JOIN FETCH ja.jobPost jp " +
            "JOIN FETCH ja.companyProfile " +
            "WHERE jp.jobId = :jobId " +
            "AND jp.companyProfile.companyProfileId = :companyProfileId " +
            "AND (:statusId IS NULL OR ja.statusIdStr = CAST(:statusId AS string)) " +
            "AND (:timeFilterDate IS NULL OR ja.appliedDate >= :timeFilterDate)")
    List<JobApplication> findByJobIdAndCompanyProfileIdWithStatusAndTimeFilter(
            @Param("jobId") Long jobId,
            @Param("companyProfileId") Long companyProfileId,
            @Param("statusId") Integer statusId,
            @Param("timeFilterDate") Timestamp timeFilterDate
    );

    @Query("SELECT COUNT(ja) FROM JobApplication ja " +
            "WHERE ja.jobPost.jobId = :jobId " +
            "AND ja.statusIdStr = CAST(:statusId AS string)")
    long countByJobPostJobIdAndStatus(
            @Param("jobId") Long jobId,
            @Param("statusId") Integer statusId
    );
}