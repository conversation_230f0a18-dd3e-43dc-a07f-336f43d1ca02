package com.job.jobportal.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
public class WorkExperience {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long workExperienceId;

    @NotBlank(message = "{msg.validation.job_title_required}")
    private String jobTitle;

    @NotBlank(message = "{msg.validation.company_required}")
    private String company;

    @NotNull(message = "{msg.validation.work_start_date_required}")
    private LocalDate startDate;

    private LocalDate endDate;
    private String responsibilities;
    private String achievements;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resumeId", nullable = false)
    private Resume resume;
}