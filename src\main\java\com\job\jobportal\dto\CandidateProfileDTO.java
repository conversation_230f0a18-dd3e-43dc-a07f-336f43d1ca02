package com.job.jobportal.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CandidateProfileDTO {
    private Long candidateProfileId;
    private Long userId;
    private String fullName;
    private String phoneNumber;
    private String resumeUrl;
    private String website;
    private List<String> skillsNames;
    private List<Integer> skillIds;
    private String jobTitle;
    private String jobCategories;
    private String jobCategoryName;
    private String jobSubCategories;
    private String jobSubCategoryName;
    private String jobSubSubCategories;
    private String jobSubSubCategoryName;
    private Long expectedSalary;
    private String salaryCurrency;
    private String currentUserEmail;
    private String yearsOfExperience;
    private String education;
    private String location;
    private Boolean isActive;
    private Long currentSalary;
    private String designation;
    private String description;
    private String language;
    private String experience;
    private String experienceName;
    private String socialLinkedIn;
    private String socialFacebook;
    private String socialTwitter;
    private String socialGlassDoor;
    private String addressCountry;
    private String addressCity;
    private String district;
    private String addressLineOne;
    private String addressLineTwo;
    private String addressPincode;

    private String profilePictureURL;
    private String profilePictureKey;
    private String updatedToken;
    private Double candidateAddressMapLocationLattitude;
    private Double candidateAddressMapLocationLongtitude;
}
