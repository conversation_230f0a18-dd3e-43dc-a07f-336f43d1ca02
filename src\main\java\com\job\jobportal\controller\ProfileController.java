package com.job.jobportal.controller;

import com.job.jobportal.dto.ProfileDTO;
import com.job.jobportal.model.Profile;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.ProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class ProfileController {

    @Autowired
    ProfileService profileService;

    private static final Logger logger = LoggerFactory.getLogger(ProfileController.class);


    @GetMapping("/profile")
    public ResponseEntity<?> getAllProfileDetails() {

        try {
         //   List<AddFamily> profile = profileService.getAllProfileDetails();
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    "msg.profile_details_get_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }


    @GetMapping("/profile/{profileId}")
    public ResponseEntity<?> getProfileDetails(@PathVariable("profileId") Long profileId) {

        try {
            ProfileDTO profile = profileService.getProfileDetails(profileId);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, profile,
                    "msg.profile_details_get_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PostMapping("/profile")
    public ResponseEntity<?> addProfileDetails(@RequestBody ProfileDTO profileDTO) {

        try {
            Profile profile = profileService.addProfile(profileDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, profile,
                    "msg.profile_details_added_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }


    @PutMapping("/profile")
    public ResponseEntity<?> updateProfileDetails(@RequestBody ProfileDTO profileDTO) {

        try {
            Profile profile = profileService.updateProfile(profileDTO);
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, profile,
                    "msg.profile_details_updated_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }
}
