package com.job.jobportal.repository;

import com.job.jobportal.model.WorkExperience;
import jakarta.data.repository.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface WorkExperienceRepository extends JpaRepository<WorkExperience, Long> {
    @Query("SELECT w FROM WorkExperience w WHERE w.resume.resumeId = :resumeId AND w.workExperienceId = :workId")
    Optional<WorkExperience> findByResumeIdAndWorkId(
            @Param("resumeId") Long resumeId,
            @Param("workId") Long workId
    );
}