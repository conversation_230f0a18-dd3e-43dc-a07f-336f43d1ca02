package com.job.jobportal.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.job.jobportal.model.CompanyProfile;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter
public class CompanyProfileResponse {
    private String authorizedPersonFirstName;
    private String authorizedPersonLastName;
    private String authorizedPersonDesignation;
    private String companyRegistrationNumber;
    private boolean isAuthorizedPerson;

    private Date companyEstYear;

    private Long companyProfileId;
    private String companyName;
    private String companyLogoURL;
    private String companyCoverURL;
    private String companyEmail;
    private String companyWebsite;
    private int companyTeamSize;
    private JsonNode companyDescription;
    private String companySocialLinkedIn;
    private String companySocialFacebook;
    private String companySocialTwitter;
    private String companySocialGlassDoor;
    private String companyAddressCountry;
    private String companyPhoneNumber;
    private Timestamp postedDate;
    private Timestamp updatedDate;
    private int companyAllowInSearch;
    private int companyNatureOfBusiness;
    private String companyAddressCity;
    private String companyAddressDistrict;
    private String companyAddressLineOne;
    private String companyAddressLineTwo;
    private String companyAddressPincode;
    private String companyAddressMapLocation;
    private Double companyAddressMapLocationLattitude;
    private Double companyAddressMapLocationLongtitude;
    private boolean companyIsActive;
    private boolean companyIsArchived;
    private String companyAddressState;
    private String currentUserEmail;
    private String emailDomainHost;
    private String emailDomainUsername;

    private Long activeJobCount;

    private String token;

    public CompanyProfileResponse(CompanyProfile profile, String token) {
        this.authorizedPersonFirstName = profile.getAuthorizedPersonFirstName();
        this.authorizedPersonLastName = profile.getAuthorizedPersonLastName();
        this.authorizedPersonDesignation = profile.getAuthorizedPersonDesignation();
        this.companyRegistrationNumber = profile.getCompanyRegistrationNumber();
        this.isAuthorizedPerson = profile.isAuthorizedPerson();

        this.companyProfileId = profile.getCompanyProfileId();
        this.companyName = profile.getCompanyName();
        this.companyLogoURL = profile.getCompanyLogoURL();
        this.companyCoverURL = profile.getCompanyCoverURL();
        this.companyEmail = profile.getCompanyEmail();
        this.companyWebsite = profile.getCompanyWebsite();
        this.companyTeamSize = profile.getCompanyTeamSize();
        this.companyEstYear = profile.getCompanyEstYear();
        this.companyDescription = profile.getCompanyDescription();
        this.companySocialLinkedIn = profile.getCompanySocialLinkedIn();
        this.companySocialFacebook = profile.getCompanySocialFacebook();
        this.companySocialTwitter = profile.getCompanySocialTwitter();
        this.companySocialGlassDoor = profile.getCompanySocialGlassDoor();
        this.companyAddressCountry = profile.getCompanyAddressCountry();
        this.companyPhoneNumber = profile.getCompanyPhoneNumber();
        this.postedDate = profile.getPostedDate();
        this.updatedDate = profile.getUpdatedDate();
        this.companyAllowInSearch = profile.getCompanyAllowInSearch();
        this.companyNatureOfBusiness = profile.getCompanyNatureOfBusiness();
        this.companyAddressCity = profile.getCompanyAddressCity();
        this.companyAddressDistrict = profile.getCompanyAddressDistrict();
        this.companyAddressLineOne = profile.getCompanyAddressLineOne();
        this.companyAddressLineTwo = profile.getCompanyAddressLineTwo();
        this.companyAddressPincode = profile.getCompanyAddressPincode();
        this.companyAddressMapLocationLattitude = profile.getCompanyAddressMapLocationLattitude();
        this.companyAddressMapLocationLongtitude = profile.getCompanyAddressMapLocationLongtitude();
        this.companyIsActive = profile.isCompanyIsActive();
        this.companyIsArchived = profile.isCompanyIsArchived();
        this.companyAddressState=profile.getCompanyAddressState();
        this.currentUserEmail=profile.getCurrentUserEmail();
        this.emailDomainHost = profile.getEmailDomainHost();
        this.emailDomainUsername = profile.getEmailDomainUsername();
        this.activeJobCount = profile.getTransientActiveJobCount();

        this.token = token;
    }
}