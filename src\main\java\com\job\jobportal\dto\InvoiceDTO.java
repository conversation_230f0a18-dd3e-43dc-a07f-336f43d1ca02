package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDTO {
    private String id;
    private String number;
    private String status;
    private Long amountDue;
    private Long amountPaid;
    private Long amountRemaining;
    private String currency;
    private Date created;
    private Date dueDate;
    private String hostedInvoiceUrl;
    private String invoicePdf;
    private String customerEmail;
    private String customerName;
    private String description;
    private String collectionMethod;
    private Long total;
    private Long subtotal;
    private Date periodStart;
    private Date periodEnd;
}
