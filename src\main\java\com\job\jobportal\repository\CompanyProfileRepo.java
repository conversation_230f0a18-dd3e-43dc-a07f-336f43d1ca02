package com.job.jobportal.repository;

import com.job.jobportal.model.CompanyProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CompanyProfileRepo extends JpaRepository<CompanyProfile, Long> {

    Optional<CompanyProfile> findByCompanyProfileIdAndCompanyIsArchivedFalseAndCompanyIsActiveTrue(Long companyProfileId);

    @Query("SELECT cp FROM CompanyProfile cp WHERE " +
            "(:isArchived IS NULL OR cp.companyIsArchived = :isArchived) AND " +
            "(:isActive IS NULL OR cp.companyIsActive = :isActive) AND " +
            "(:companyName IS NULL OR LOWER(TRIM(cp.companyName)) LIKE LOWER(CONCAT('%', TRIM(:companyName), '%'))) AND " +
            "(:companyAddressCity IS NULL OR LOWER(TRIM(cp.companyAddressCity)) LIKE LOWER(CONCAT('%', TRIM(:companyAddressCity), '%'))) ")
    Page<CompanyProfile> findAllByFilters(
            @Param("isArchived") Boolean isArchived,
            @Param("isActive") Boolean isActive,
            @Param("companyName") String companyName,
            @Param("companyAddressCity") String companyAddressCity,
            Pageable pageable
    );
}
