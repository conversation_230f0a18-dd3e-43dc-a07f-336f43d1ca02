package com.job.jobportal.model;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
public class AccountDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long accountDetailsId;

    private int isActive;

    private int isPremiumAccount;

    private int isDeleteScheduled;

    private Timestamp deleteScheduleDate;


    private Long storageUse;

}
