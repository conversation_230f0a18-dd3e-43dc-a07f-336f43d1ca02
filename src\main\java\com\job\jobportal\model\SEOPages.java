package com.job.jobportal.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;

@Entity
@Data
public class SEOPages {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long seoPagesId;

    private String seoPagesTitle;

    private String seoPagesUrl;

    @Column(columnDefinition = "LONGTEXT")
    private String metaDescription;

    private String metaKeyWord;

    @Column(columnDefinition = "LONGTEXT")
    private String featuredImage;

    @Column(columnDefinition = "LONGTEXT")
    private String thumbnailImage;

    private String featuredImageKey;

    private String thumbnailImageKey;


    private Integer categoryId;

    private Integer subcategoryId;

    private Integer subSubcategoryId;

    private String customSlug;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private JsonNode seoPagesDescription;


    private String seoPagesTags;

    private Timestamp seoPagesCreatedDate;

}
