package com.job.jobportal.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class JobCreationResponse {
    private JobDTO job;
    private Map<String, List<MasterDataEntry>> masterData;

    public JobCreationResponse(JobDTO job, Map<String, List<MasterDataEntry>> masterData) {
        this.job = job;
        this.masterData = masterData;
    }
}