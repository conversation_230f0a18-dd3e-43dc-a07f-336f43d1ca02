package com.job.jobportal.util;

import com.job.jobportal.security.CustomUserDetailsService;
import com.job.jobportal.security.UserPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.Calendar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class CommonUtils {
    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);
    private static CustomUserDetailsService customUserDetailsService;

    @Autowired
    public void setCustomUserDetailsService(CustomUserDetailsService service) {
        CommonUtils.customUserDetailsService = service;
    }

    public static Timestamp getCurrentTime() {
        return new Timestamp(Calendar.getInstance().getTimeInMillis());
    }

    public static UserPrincipal getUserPrincipal() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!(principal instanceof UserPrincipal)) {
            throw new RuntimeException("Authentication error: User not properly authenticated");
        }
        return (UserPrincipal) principal;
    }

    public static UserPrincipal refreshUserPrincipal(Long userId) {
        return (UserPrincipal) customUserDetailsService.loadUserById(userId);
    }

    public static int generateRandomNumber(int min, int max) {
        int randomNum = min + (int) (Math.random() * (max - min + 1));
        return randomNum;
    }

    public static BigDecimal bytesToGigabytesBigDecimal(long bytes) {
        BigDecimal bytesBD = new BigDecimal(bytes);
        BigDecimal factor = new BigDecimal(1073741824);
        return bytesBD.divide(factor, 1, RoundingMode.HALF_UP);  // Rounding to 1 decimal place
    }

    public static BigDecimal bytesToMegabytesBigDecimal(long bytes) {
        BigDecimal bytesBD = new BigDecimal(bytes);
        BigDecimal factor = new BigDecimal(1048576);  // 1 MB = 2^20 bytes
        return bytesBD.divide(factor, 1, RoundingMode.HALF_UP);  // Rounding to 1 decimal place
    }

    public static String normalizeText(String text) {
        if (text == null) {
            return null;
        }
        logger.debug("Normalizing text: [{}]", text);
        String normalized = text.trim().replaceAll("\\s+", " ");
        logger.debug("Normalized result: [{}]", normalized);
        return normalized;
    }

    public static Long bytesToGigabytesLong(long bytes) {
        BigDecimal bytesBD = new BigDecimal(bytes);
        BigDecimal factor = new BigDecimal(1073741824);
        BigDecimal result = bytesBD.divide(factor, 0, RoundingMode.HALF_UP);
        return result.longValue();
    }

    public static double bytesToGigabytesDecimal(long bytes) {
        BigDecimal bytesBD = new BigDecimal(bytes);
        BigDecimal factor = new BigDecimal(1073741824);
        BigDecimal result = bytesBD.divide(factor, 1, RoundingMode.HALF_UP); // Set scale to 1 for one decimal place
        return result.doubleValue();
    }


}
