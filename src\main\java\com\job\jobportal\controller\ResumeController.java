package com.job.jobportal.controller;

import com.job.jobportal.dto.*;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.ResourceNotFoundException;
import com.job.jobportal.service.ResumeService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/api/resumes")
public class ResumeController {

    private static final Logger logger = LoggerFactory.getLogger(ResumeController.class);

    @Autowired
    private ResumeService resumeService;

    @Autowired
    private MessageSource messageSource;

    @PostMapping
    public ResponseEntity<?> createResume(
            @Valid @RequestBody ResumeDTO resumeDTO,
            Locale locale) {
        try {
            ResumeDTO createdResume = resumeService.createResume(resumeDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(
                    new ApiResponse<>(HttpStatus.CREATED, true, createdResume,
                            messageSource.getMessage("msg.resume.created", null, locale))
            );
        } catch (Exception e) {
            logger.error("Error creating resume: {}", e.getMessage());
            return handleException(e, locale);
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllResumes(Locale locale) {
        try {
            List<ResumeDTO> resumes = resumeService.getAllResumes();
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, resumes,
                    messageSource.getMessage("msg.resumes.fetched", null, locale))
            );
        } catch (Exception e) {
            logger.error("Error fetching resumes: {}", e.getMessage());
            return handleException(e, locale);
        }
    }

    @PutMapping("/{resumeId}")
    public ResponseEntity<?> updateResume(
            @PathVariable Long resumeId,
            @RequestBody ResumeDTO resumeDTO,
            Locale locale) {
        try {
            resumeDTO.setResumeId(resumeId);
            ResumeDTO updatedResume = resumeService.updateResume(resumeId, resumeDTO);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, updatedResume,
                    messageSource.getMessage("msg.resume.updated", null, locale))
            );
        } catch (Exception e) {
            logger.error("Error updating resume {}: {}", resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @GetMapping("/{resumeId}/delete-token")
    public ResponseEntity<?> requestDeleteResume(
            @PathVariable Long resumeId,
            Locale locale) {
        try {
            String token = resumeService.requestDeleteResume(resumeId);
            logger.info("Delete token requested for resume ID: {}", resumeId);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, token,
                    messageSource.getMessage("msg.delete_token_generated", null, "msg.delete_token_generated", locale)));
        } catch (Exception e) {
            logger.error("Error requesting delete token for resume {}: {}", resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @DeleteMapping("/{resumeId}")
    public ResponseEntity<?> deleteResume(
            @PathVariable Long resumeId,
            @RequestParam("token") String confirmationToken,
            Locale locale) {
        try {
            resumeService.deleteResume(resumeId, confirmationToken);
            logger.info("Resume {} deleted with confirmation token", resumeId);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null,
                    messageSource.getMessage("msg.resume.deleted", null, locale)));
        } catch (Exception e) {
            logger.error("Error deleting resume {}: {}", resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @PostMapping("/{resumeId}/education")
    public ResponseEntity<?> addEducation(
            @PathVariable Long resumeId,
            @RequestBody EducationDTO educationDTO,
            Locale locale) {
        try {
            EducationDTO createdEducation = resumeService.addEducation(resumeId, educationDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(new ApiResponse<>(HttpStatus.CREATED, true, createdEducation,
                    messageSource.getMessage("msg.education.added", null, locale)));
        } catch (Exception e) {
            logger.error("Error adding education to resume {}: {}", resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @PutMapping("/{resumeId}/education/{educationId}")
    public ResponseEntity<?> updateEducation(
            @PathVariable Long resumeId,
            @PathVariable Long educationId,
            @RequestBody EducationDTO educationDTO,
            Locale locale) {
        try {
            educationDTO.setEducationId(educationId);
            EducationDTO updatedEducation = resumeService.updateEducation(resumeId, educationId, educationDTO);
            return ResponseEntity.ok(
                    new ApiResponse<>(HttpStatus.OK, true, updatedEducation,
                            messageSource.getMessage("msg.education.updated", null, locale)));
        } catch (Exception e) {
            logger.error("Error updating education {} in resume {}: {}", educationId, resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @DeleteMapping("/{resumeId}/education/{educationId}")
    public ResponseEntity<?> deleteEducation(
            @PathVariable Long resumeId,
            @PathVariable Long educationId,
            Locale locale) {
        try {
            resumeService.deleteEducation(resumeId, educationId);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null,
                    messageSource.getMessage("msg.education.deleted", null, locale)));
        } catch (Exception e) {
            logger.error("Error deleting education {} from resume {}: {}", educationId, resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @PostMapping("/{resumeId}/work-experience")
    public ResponseEntity<?> addWorkExperience(
            @PathVariable Long resumeId,
            @RequestBody WorkExperienceDTO workDTO,
            Locale locale) {
        try {
            WorkExperienceDTO createdWork = resumeService.addWorkExperience(resumeId, workDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(new ApiResponse<>(HttpStatus.CREATED, true, createdWork,
                    messageSource.getMessage("msg.work_experience.added", null, locale)));
        } catch (Exception e) {
            logger.error("Error adding work experience to resume {}: {}", resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @PutMapping("/{resumeId}/work-experience/{workId}")
    public ResponseEntity<?> updateWorkExperience(
            @PathVariable Long resumeId,
            @PathVariable Long workId,
            @RequestBody WorkExperienceDTO workDTO,
            Locale locale) {
        try {
            workDTO.setWorkExperienceId(workId);
            WorkExperienceDTO updatedWork = resumeService.updateWorkExperience(resumeId, workId, workDTO);
            return ResponseEntity.ok(
                    new ApiResponse<>(HttpStatus.OK, true, updatedWork,
                            messageSource.getMessage("msg.work_experience.updated", null, locale)));
        } catch (Exception e) {
            logger.error("Error updating work experience {} in resume {}: {}", workId, resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @DeleteMapping("/{resumeId}/work-experience/{workId}")
    public ResponseEntity<?> deleteWorkExperience(
            @PathVariable Long resumeId,
            @PathVariable Long workId,
            Locale locale) {
        try {
            resumeService.deleteWorkExperience(resumeId, workId);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null,
                    messageSource.getMessage("msg.work_experience.deleted", null, locale)));
        } catch (Exception e) {
            logger.error("Error deleting work experience {} from resume {}: {}", workId, resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @PostMapping("/{resumeId}/awards")
    public ResponseEntity<?> addAward(
            @PathVariable Long resumeId,
            @RequestBody AwardDTO awardDTO,
            Locale locale) {
        try {
            AwardDTO createdAward = resumeService.addAward(resumeId, awardDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(new ApiResponse<>(HttpStatus.CREATED, true, createdAward,
                    messageSource.getMessage("msg.award.added", null, locale)));
        } catch (Exception e) {
            logger.error("Error adding award to resume {}: {}", resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @PutMapping("/{resumeId}/awards/{awardId}")
    public ResponseEntity<?> updateAward(
            @PathVariable Long resumeId,
            @PathVariable Long awardId,
            @RequestBody AwardDTO awardDTO,
            Locale locale) {
        try {
            awardDTO.setAwardId(awardId);
            AwardDTO updatedAward = resumeService.updateAward(resumeId, awardId, awardDTO);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, updatedAward,
                    messageSource.getMessage("msg.award.updated", null, locale)));
        } catch (Exception e) {
            logger.error("Error updating award {} in resume {}: {}", awardId, resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    @DeleteMapping("/{resumeId}/awards/{awardId}")
    public ResponseEntity<?> deleteAward(
            @PathVariable Long resumeId,
            @PathVariable Long awardId,
            Locale locale) {
        try {
            resumeService.deleteAward(resumeId, awardId);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null,
                    messageSource.getMessage("msg.award.deleted", null, locale)));
        } catch (Exception e) {
            logger.error("Error deleting award {} from resume {}: {}", awardId, resumeId, e.getMessage());
            return handleException(e, locale);
        }
    }

    private ResponseEntity<?> handleException(Exception e, Locale locale) {
        HttpStatus status = (e instanceof ResourceNotFoundException) ? HttpStatus.NOT_FOUND : HttpStatus.BAD_REQUEST;
        String message;
        if(status==HttpStatus.BAD_REQUEST){
            message = messageSource.getMessage(e.getMessage(), null, "msg.something_went_wrong", locale);
        }else{
            message = messageSource.getMessage(e.getMessage(), null, "msg.retry", locale);
        }
        logger.error("Exception occurred: {} - Status: {}", e.getMessage(), status, e);

        return ResponseEntity.status(status).body(
                new ApiResponse<>(status, false, null, message));
    }
}