package com.job.jobportal.controller;

import com.job.jobportal.dto.ChangePasswordDTO;
import com.job.jobportal.dto.ChangeUserDetailsDTO;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.security.AuthProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SettingController {


    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private static final Logger logger = LoggerFactory.getLogger(SettingController.class);


    @PutMapping("/changepassword")
    public ResponseEntity<?> changepassword(@RequestBody ChangePasswordDTO changePasswordDTO) {

        try {

            UserPrincipal p = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            Registereduser registereduser = userService.findByEmail(p.getEmail()).get();

            if (passwordEncoder.matches(changePasswordDTO.getOldPassword(), registereduser.getPassword())) {
                if (changePasswordDTO.getNewPassword().equalsIgnoreCase(changePasswordDTO.getConfirmNewPassword())) {
                    // Creating user's account
                    userService.updatePassword(passwordEncoder.encode(changePasswordDTO.getNewPassword()), p.getId());
                } else {
                    throw new Exception("msg.password_mismatch");
                }
            } else {
                throw new Exception("msg.password_mismatch");
            }
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    "msg.password_change_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PutMapping("/addpassword")
    public ResponseEntity<?> addpassword(@RequestBody ChangePasswordDTO changePasswordDTO) {

        try {

            UserPrincipal p = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            Registereduser registereduser = userService.findByEmail(p.getEmail()).get();
            userService.updatePasswordWithAuthType(passwordEncoder.encode(changePasswordDTO.getNewPassword()), p.getId(), AuthProvider.local);

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    "msg.password_add_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }

    @PutMapping("/userdetails")
    public ResponseEntity<?> changeUserDetails(@RequestBody ChangeUserDetailsDTO changeUserDetailsDTO) {

        try {

            UserPrincipal p = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            userService.updateUserDetails(changeUserDetailsDTO, p.getId());

            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,
                    "msg.userdetails_changed_success"), HttpStatus.OK);
        } catch (BadRequestException e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);

        }

    }
}
