package com.job.jobportal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class WorkExperienceDTO {
    private Long workExperienceId;

    @NotBlank(message = "msg.validation.job_title_required")
    private String jobTitle;

    @NotBlank(message = "msg.validation.company_required")
    private String company;

    @NotNull(message = "msg.validation.work_start_date_required")
    private LocalDate startDate;
    private LocalDate endDate;
    private String responsibilities;
    private String achievements;
}